"""
认证服务
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, Tu<PERSON>
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import secrets
import pyotp
import qrcode
import io
import base64
import logging

from ..models.user import User, UserSession
from ..config import settings
from ..database import redis_client

logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """认证服务类"""
    
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.algorithm
        self.access_token_expire_minutes = settings.access_token_expire_minutes
        self.refresh_token_expire_days = settings.refresh_token_expire_days
    
    # 密码相关方法
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """生成密码哈希"""
        return pwd_context.hash(password)
    
    def validate_password_strength(self, password: str) -> Tuple[bool, str]:
        """验证密码强度"""
        if len(password) < 8:
            return False, "密码长度至少8位"
        
        if not any(c.isupper() for c in password):
            return False, "密码必须包含大写字母"
        
        if not any(c.islower() for c in password):
            return False, "密码必须包含小写字母"
        
        if not any(c.isdigit() for c in password):
            return False, "密码必须包含数字"
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            return False, "密码必须包含特殊字符"
        
        return True, "密码强度符合要求"
    
    # JWT令牌相关方法
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                return None
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp and datetime.utcnow() > datetime.fromtimestamp(exp):
                return None
            
            return payload
            
        except JWTError as e:
            logger.warning(f"JWT验证失败: {e}")
            return None
    
    async def is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        try:
            result = await redis_client.get(f"blacklist:{token}")
            return result is not None
        except Exception as e:
            logger.error(f"检查令牌黑名单失败: {e}")
            return False
    
    async def blacklist_token(self, token: str, expire_time: int = None):
        """将令牌加入黑名单"""
        try:
            if expire_time is None:
                expire_time = self.access_token_expire_minutes * 60
            
            await redis_client.setex(f"blacklist:{token}", expire_time, "1")
            logger.info("令牌已加入黑名单")
        except Exception as e:
            logger.error(f"令牌加入黑名单失败: {e}")
    
    # 用户认证方法
    async def authenticate_user(
        self, 
        db: AsyncSession, 
        username: str, 
        password: str,
        ip_address: str = None
    ) -> Optional[User]:
        """用户认证"""
        try:
            # 查询用户
            stmt = select(User).where(
                (User.username == username) | (User.email == username)
            )
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning(f"用户不存在: {username}")
                return None
            
            # 检查用户状态
            if not user.is_active:
                logger.warning(f"用户已被禁用: {username}")
                return None
            
            # 检查账户锁定
            if user.locked_until and user.locked_until > datetime.utcnow():
                logger.warning(f"用户账户被锁定: {username}")
                return None
            
            # 验证密码
            if not self.verify_password(password, user.password_hash):
                # 增加失败次数
                user.failed_login_attempts += 1
                
                # 检查是否需要锁定账户
                if user.failed_login_attempts >= 5:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                    logger.warning(f"用户账户因多次失败登录被锁定: {username}")
                
                await db.commit()
                return None
            
            # 重置失败次数
            user.failed_login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            
            await db.commit()
            
            logger.info(f"用户认证成功: {username}")
            return user
            
        except Exception as e:
            logger.error(f"用户认证异常: {e}")
            return None
    
    async def create_user_session(
        self,
        db: AsyncSession,
        user: User,
        device_info: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> Tuple[str, str]:
        """创建用户会话"""
        try:
            # 生成令牌
            token_data = {
                "sub": str(user.id),
                "username": user.username,
                "email": user.email,
                "is_admin": user.is_admin
            }
            
            access_token = self.create_access_token(token_data)
            refresh_token = self.create_refresh_token({"sub": str(user.id)})
            
            # 创建会话记录
            session = UserSession(
                user_id=user.id,
                session_token=access_token,
                refresh_token=refresh_token,
                device_info=device_info,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
            )
            
            db.add(session)
            await db.commit()
            
            logger.info(f"用户会话创建成功: {user.username}")
            return access_token, refresh_token
            
        except Exception as e:
            logger.error(f"创建用户会话异常: {e}")
            raise
    
    async def refresh_access_token(
        self,
        db: AsyncSession,
        refresh_token: str
    ) -> Optional[str]:
        """刷新访问令牌"""
        try:
            # 验证刷新令牌
            payload = self.verify_token(refresh_token, "refresh")
            if not payload:
                return None
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            # 查询用户
            stmt = select(User).where(User.id == int(user_id))
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user or not user.is_active:
                return None
            
            # 生成新的访问令牌
            token_data = {
                "sub": str(user.id),
                "username": user.username,
                "email": user.email,
                "is_admin": user.is_admin
            }
            
            new_access_token = self.create_access_token(token_data)
            
            logger.info(f"访问令牌刷新成功: {user.username}")
            return new_access_token
            
        except Exception as e:
            logger.error(f"刷新访问令牌异常: {e}")
            return None
    
    async def logout_user(self, db: AsyncSession, token: str):
        """用户登出"""
        try:
            # 将令牌加入黑名单
            await self.blacklist_token(token)
            
            # 更新会话状态
            stmt = select(UserSession).where(UserSession.session_token == token)
            result = await db.execute(stmt)
            session = result.scalar_one_or_none()
            
            if session:
                session.is_active = False
                await db.commit()
            
            logger.info("用户登出成功")
            
        except Exception as e:
            logger.error(f"用户登出异常: {e}")
    
    # MFA相关方法
    def generate_mfa_secret(self) -> str:
        """生成MFA密钥"""
        return pyotp.random_base32()
    
    def generate_mfa_qr_code(self, user_email: str, secret: str) -> str:
        """生成MFA二维码"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=settings.app_name
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_mfa_token(self, secret: str, token: str) -> bool:
        """验证MFA令牌"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=1)
        except Exception as e:
            logger.error(f"MFA令牌验证异常: {e}")
            return False
    
    def generate_backup_codes(self, count: int = 10) -> list:
        """生成备用代码"""
        return [secrets.token_hex(4).upper() for _ in range(count)]


# 创建认证服务实例
auth_service = AuthService()

"""
FastAPI主应用
"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import logging
import time
import uvicorn

from .config import settings
from .database import init_database, db_manager
from .middleware import (
    SecurityMiddleware,
    RateLimitMiddleware,
    LoggingMiddleware,
    MetricsMiddleware
)

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format=settings.log_format,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log") if not settings.debug else logging.NullHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动企业级文件共享系统...")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库初始化完成")
        
        # 检查健康状态
        health = await db_manager.health_check()
        logger.info(f"📊 系统健康检查: {health}")
        
        if not all(health.values()):
            logger.warning("⚠️ 部分服务连接失败，请检查配置")
        
        logger.info("🎉 系统启动完成")
        
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🔄 正在关闭系统...")
    try:
        await db_manager.close_connections()
        logger.info("✅ 系统关闭完成")
    except Exception as e:
        logger.error(f"❌ 系统关闭时出错: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description="企业级文件共享管理系统 - 支持内外网访问控制、双搜索引擎、多级权限管理",
    version=settings.app_version,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
    lifespan=lifespan
)

# 添加中间件
# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 信任主机中间件
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境应该配置具体的主机名
    )

# 自定义中间件
app.add_middleware(SecurityMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(LoggingMiddleware)

if settings.enable_metrics:
    app.add_middleware(MetricsMiddleware)

# 静态文件服务
if settings.debug:
    app.mount("/static", StaticFiles(directory="static"), name="static")


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": time.time(),
            "path": str(request.url)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "内部服务器错误" if not settings.debug else str(exc),
            "status_code": 500,
            "timestamp": time.time(),
            "path": str(request.url)
        }
    )


# 健康检查端点
@app.get("/health", tags=["系统"])
async def health_check():
    """系统健康检查"""
    try:
        health = await db_manager.health_check()
        
        return {
            "status": "healthy" if all(health.values()) else "degraded",
            "timestamp": time.time(),
            "version": settings.app_version,
            "environment": settings.environment,
            "services": health,
            "uptime": time.time()  # 简化的运行时间
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": time.time(),
                "error": str(e)
            }
        )


@app.get("/", tags=["系统"])
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.app_name}",
        "version": settings.app_version,
        "environment": settings.environment,
        "docs_url": "/docs" if settings.debug else None,
        "timestamp": time.time()
    }


# 系统信息端点
@app.get("/info", tags=["系统"])
async def system_info():
    """系统信息"""
    return {
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "debug": settings.debug,
        "features": {
            "dual_search_engine": True,
            "network_access_control": True,
            "multi_factor_auth": True,
            "real_time_monitoring": True,
            "ai_image_search": True
        },
        "limits": {
            "max_file_size": settings.max_file_size,
            "allowed_extensions": settings.allowed_extensions,
            "rate_limit_per_minute": settings.rate_limit_per_minute
        }
    }


# 导入路由
from .routers import auth, files, search, admin, users, monitoring

# 注册路由
app.include_router(
    auth.router,
    prefix="/api/auth",
    tags=["认证授权"]
)

app.include_router(
    users.router,
    prefix="/api/users",
    tags=["用户管理"]
)

app.include_router(
    files.router,
    prefix="/api/files",
    tags=["文件管理"]
)

app.include_router(
    search.router,
    prefix="/api/search",
    tags=["搜索引擎"]
)

app.include_router(
    admin.router,
    prefix="/api/admin",
    tags=["系统管理"]
)

app.include_router(
    monitoring.router,
    prefix="/api/monitoring",
    tags=["监控统计"]
)


# 开发服务器启动函数
def start_dev_server():
    """启动开发服务器"""
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=settings.debug,
        workers=1 if settings.debug else settings.workers
    )


if __name__ == "__main__":
    start_dev_server()

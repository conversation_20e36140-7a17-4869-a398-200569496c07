"""
认证路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
import logging

from ..database import get_db
from ..services.auth_service import auth_service
from ..models.user import User
from ..utils.dependencies import get_current_user, get_current_active_user

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


# Pydantic模型
class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str
    remember_me: bool = False
    mfa_token: Optional[str] = None


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    current_password: str
    new_password: str
    confirm_password: str


class EnableMFAResponse(BaseModel):
    """启用MFA响应模型"""
    secret: str
    qr_code: str
    backup_codes: list


class VerifyMFARequest(BaseModel):
    """验证MFA请求模型"""
    token: str


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    request: Request,
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    用户登录接口
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    - **mfa_token**: 多因素认证令牌（如果启用MFA）
    """
    try:
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("User-Agent", "")
        
        # 用户认证
        user = await auth_service.authenticate_user(
            db, 
            login_data.username, 
            login_data.password,
            client_ip
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查MFA
        if user.mfa_enabled:
            if not login_data.mfa_token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="需要提供多因素认证令牌"
                )
            
            if not auth_service.verify_mfa_token(user.mfa_secret, login_data.mfa_token):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="多因素认证令牌无效"
                )
        
        # 创建会话
        device_info = {
            "user_agent": user_agent,
            "platform": request.headers.get("Sec-Ch-Ua-Platform", "unknown"),
            "mobile": request.headers.get("Sec-Ch-Ua-Mobile", "?0") == "?1"
        }
        
        access_token, refresh_token = await auth_service.create_user_session(
            db, user, device_info, client_ip, user_agent
        )
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=auth_service.access_token_expire_minutes * 60,
            user=user.to_dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", summary="刷新访问令牌")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        new_access_token = await auth_service.refresh_access_token(
            db, refresh_data.refresh_token
        )
        
        if not new_access_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌无效或已过期"
            )
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": auth_service.access_token_expire_minutes * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    用户登出
    """
    try:
        token = credentials.credentials
        await auth_service.logout_user(db, token)
        
        return {"message": "登出成功"}
        
    except Exception as e:
        logger.error(f"登出异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败"
        )


@router.get("/me", summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前登录用户的详细信息
    """
    return {
        "user": current_user.to_dict(),
        "permissions": {
            "is_admin": current_user.is_admin,
            "can_upload": True,
            "can_download": True,
            "can_delete": current_user.is_admin,
            "can_share": True
        }
    }


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    修改用户密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码
    - **confirm_password**: 确认新密码
    """
    try:
        # 验证当前密码
        if not auth_service.verify_password(password_data.current_password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )
        
        # 验证新密码确认
        if password_data.new_password != password_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码与确认密码不匹配"
            )
        
        # 验证密码强度
        is_valid, message = auth_service.validate_password_strength(password_data.new_password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 更新密码
        current_user.password_hash = auth_service.get_password_hash(password_data.new_password)
        current_user.last_password_change = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"用户密码修改成功: {current_user.username}")
        
        return {"message": "密码修改成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.post("/enable-mfa", response_model=EnableMFAResponse, summary="启用多因素认证")
async def enable_mfa(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启用多因素认证
    """
    try:
        if current_user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="多因素认证已启用"
            )
        
        # 生成MFA密钥
        secret = auth_service.generate_mfa_secret()
        qr_code = auth_service.generate_mfa_qr_code(current_user.email, secret)
        backup_codes = auth_service.generate_backup_codes()
        
        # 暂存密钥（用户验证后才正式启用）
        current_user.mfa_secret = secret
        current_user.backup_codes = backup_codes
        
        await db.commit()
        
        return EnableMFAResponse(
            secret=secret,
            qr_code=qr_code,
            backup_codes=backup_codes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启用MFA异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启用多因素认证失败"
        )


@router.post("/verify-mfa", summary="验证并启用多因素认证")
async def verify_and_enable_mfa(
    mfa_data: VerifyMFARequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    验证MFA令牌并正式启用多因素认证
    
    - **token**: MFA令牌
    """
    try:
        if current_user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="多因素认证已启用"
            )
        
        if not current_user.mfa_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先生成MFA密钥"
            )
        
        # 验证MFA令牌
        if not auth_service.verify_mfa_token(current_user.mfa_secret, mfa_data.token):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MFA令牌无效"
            )
        
        # 启用MFA
        current_user.mfa_enabled = True
        await db.commit()
        
        logger.info(f"用户启用MFA成功: {current_user.username}")
        
        return {"message": "多因素认证启用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证MFA异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证多因素认证失败"
        )


@router.post("/disable-mfa", summary="禁用多因素认证")
async def disable_mfa(
    password_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    禁用多因素认证
    
    - **password**: 当前密码确认
    """
    try:
        if not current_user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="多因素认证未启用"
            )
        
        # 验证密码
        password = password_data.get("password")
        if not password or not auth_service.verify_password(password, current_user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码错误"
            )
        
        # 禁用MFA
        current_user.mfa_enabled = False
        current_user.mfa_secret = None
        current_user.backup_codes = None
        
        await db.commit()
        
        logger.info(f"用户禁用MFA成功: {current_user.username}")
        
        return {"message": "多因素认证已禁用"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用MFA异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="禁用多因素认证失败"
        )

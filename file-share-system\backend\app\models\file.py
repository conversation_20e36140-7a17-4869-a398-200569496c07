"""
文件相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any, List
import os

from ..database import Base


class Directory(Base):
    """目录模型"""
    __tablename__ = "directories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    path = Column(String(1000), nullable=False, unique=True, index=True)
    
    # 父目录关系
    parent_id = Column(Integer, ForeignKey("directories.id"), nullable=True)
    
    # 目录属性
    is_shared = Column(Boolean, default=False)
    is_public = Column(Boolean, default=False)
    
    # 权限设置
    network_policy = Column(String(20), default="internal_external")  # internal_only, external_allowed, vpn_required
    access_permissions = Column(JSON, nullable=True)  # 访问权限配置
    
    # 统计信息
    file_count = Column(Integer, default=0)
    total_size = Column(BigInteger, default=0)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    parent = relationship("Directory", remote_side=[id], backref="children")
    files = relationship("FileEntry", back_populates="directory", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Directory(id={self.id}, name='{self.name}', path='{self.path}')>"
    
    def get_full_path(self) -> str:
        """获取完整路径"""
        return self.path
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "path": self.path,
            "parent_id": self.parent_id,
            "is_shared": self.is_shared,
            "is_public": self.is_public,
            "network_policy": self.network_policy,
            "file_count": self.file_count,
            "total_size": self.total_size,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class FileEntry(Base):
    """文件条目模型"""
    __tablename__ = "file_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False, index=True)
    original_filename = Column(String(255), nullable=False)  # 原始文件名
    
    # 文件路径和存储
    file_path = Column(String(1000), nullable=False)  # 实际存储路径
    relative_path = Column(String(1000), nullable=False)  # 相对路径
    
    # 文件属性
    file_size = Column(BigInteger, nullable=False)
    file_type = Column(String(50), nullable=False, index=True)
    mime_type = Column(String(100), nullable=True)
    file_extension = Column(String(10), nullable=False, index=True)
    
    # 文件哈希和校验
    file_hash = Column(String(64), nullable=False, index=True)  # SHA-256
    md5_hash = Column(String(32), nullable=True)  # MD5
    
    # 关联关系
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    directory_id = Column(Integer, ForeignKey("directories.id"), nullable=True)
    
    # 文件状态
    is_deleted = Column(Boolean, default=False)
    is_public = Column(Boolean, default=False)
    is_encrypted = Column(Boolean, default=False)
    
    # 版本控制
    version = Column(Integer, default=1)
    is_latest_version = Column(Boolean, default=True)
    
    # 元数据
    metadata = Column(JSON, nullable=True)  # 文件元数据（EXIF等）
    tags = Column(JSON, nullable=True)  # 文件标签
    description = Column(Text, nullable=True)
    
    # 访问统计
    download_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_accessed = Column(DateTime, nullable=True)
    
    # 关系定义
    owner = relationship("User", back_populates="files")
    directory = relationship("Directory", back_populates="files")
    versions = relationship("FileVersion", back_populates="file", cascade="all, delete-orphan")
    thumbnails = relationship("FileThumbnail", back_populates="file", cascade="all, delete-orphan")
    shares = relationship("FileShare", back_populates="file", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FileEntry(id={self.id}, filename='{self.filename}', size={self.file_size})>"
    
    def get_file_extension(self) -> str:
        """获取文件扩展名"""
        return os.path.splitext(self.filename)[1].lower()
    
    def get_human_readable_size(self) -> str:
        """获取人类可读的文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} PB"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_path": self.file_path,
            "relative_path": self.relative_path,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "file_extension": self.file_extension,
            "file_hash": self.file_hash,
            "owner_id": self.owner_id,
            "directory_id": self.directory_id,
            "is_deleted": self.is_deleted,
            "is_public": self.is_public,
            "is_encrypted": self.is_encrypted,
            "version": self.version,
            "is_latest_version": self.is_latest_version,
            "metadata": self.metadata,
            "tags": self.tags,
            "description": self.description,
            "download_count": self.download_count,
            "view_count": self.view_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
        }


class FileVersion(Base):
    """文件版本模型"""
    __tablename__ = "file_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("file_entries.id"), nullable=False)
    
    # 版本信息
    version_number = Column(Integer, nullable=False)
    version_name = Column(String(100), nullable=True)
    
    # 文件信息
    file_path = Column(String(1000), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    file_hash = Column(String(64), nullable=False)
    
    # 变更信息
    change_description = Column(Text, nullable=True)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 关系定义
    file = relationship("FileEntry", back_populates="versions")
    changed_by_user = relationship("User")
    
    def __repr__(self):
        return f"<FileVersion(id={self.id}, file_id={self.file_id}, version={self.version_number})>"


class FileThumbnail(Base):
    """文件缩略图模型"""
    __tablename__ = "file_thumbnails"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("file_entries.id"), nullable=False)
    
    # 缩略图信息
    size = Column(String(20), nullable=False)  # 150x150, 300x300, 600x600
    thumbnail_path = Column(String(1000), nullable=False)
    thumbnail_size = Column(Integer, nullable=False)  # 字节
    
    # 生成状态
    is_generated = Column(Boolean, default=False)
    generation_error = Column(Text, nullable=True)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    file = relationship("FileEntry", back_populates="thumbnails")
    
    def __repr__(self):
        return f"<FileThumbnail(id={self.id}, file_id={self.file_id}, size='{self.size}')>"


class FileShare(Base):
    """文件分享模型"""
    __tablename__ = "file_shares"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("file_entries.id"), nullable=False)
    
    # 分享信息
    share_token = Column(String(64), unique=True, nullable=False, index=True)
    share_name = Column(String(255), nullable=True)
    
    # 分享设置
    is_public = Column(Boolean, default=False)
    requires_password = Column(Boolean, default=False)
    password_hash = Column(String(255), nullable=True)
    
    # 访问控制
    max_downloads = Column(Integer, nullable=True)  # 最大下载次数
    download_count = Column(Integer, default=0)
    allowed_ips = Column(JSON, nullable=True)  # 允许的IP列表
    
    # 时间控制
    expires_at = Column(DateTime, nullable=True)
    
    # 创建者
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_accessed = Column(DateTime, nullable=True)
    
    # 关系定义
    file = relationship("FileEntry", back_populates="shares")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<FileShare(id={self.id}, file_id={self.file_id}, token='{self.share_token}')>"
    
    def is_expired(self) -> bool:
        """检查分享是否过期"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    def can_download(self) -> bool:
        """检查是否可以下载"""
        if not self.is_active or self.is_expired():
            return False
        if self.max_downloads and self.download_count >= self.max_downloads:
            return False
        return True

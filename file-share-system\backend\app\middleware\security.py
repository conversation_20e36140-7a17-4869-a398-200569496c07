"""
安全中间件
"""
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import time
import logging
import ipaddress
from typing import Dict, Set
import re

from ..config import settings

logger = logging.getLogger(__name__)


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.blocked_ips: Set[str] = set()
        self.suspicious_patterns = [
            r'<script.*?>.*?</script>',  # XSS
            r'union.*select',  # SQL注入
            r'drop.*table',  # SQL注入
            r'exec.*\(',  # 代码注入
            r'eval.*\(',  # 代码注入
        ]
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        start_time = time.time()
        
        try:
            # 获取客户端IP
            client_ip = self.get_client_ip(request)
            
            # 检查IP黑名单
            if client_ip in self.blocked_ips:
                logger.warning(f"阻止黑名单IP访问: {client_ip}")
                return JSONResponse(
                    status_code=403,
                    content={"error": "访问被拒绝", "code": "IP_BLOCKED"}
                )
            
            # 检查网络访问权限
            if not await self.check_network_access(request, client_ip):
                logger.warning(f"网络访问权限检查失败: {client_ip}")
                return JSONResponse(
                    status_code=403,
                    content={"error": "网络访问被拒绝", "code": "NETWORK_ACCESS_DENIED"}
                )
            
            # 检查恶意请求
            if await self.detect_malicious_request(request):
                logger.warning(f"检测到恶意请求: {client_ip} - {request.url}")
                self.blocked_ips.add(client_ip)
                return JSONResponse(
                    status_code=400,
                    content={"error": "恶意请求", "code": "MALICIOUS_REQUEST"}
                )
            
            # 添加安全头
            response = await call_next(request)
            response = self.add_security_headers(response)
            
            # 记录请求日志
            process_time = time.time() - start_time
            logger.info(
                f"请求处理完成: {request.method} {request.url} - "
                f"状态码: {response.status_code} - "
                f"处理时间: {process_time:.3f}s - "
                f"客户端IP: {client_ip}"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"安全中间件处理异常: {e}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={"error": "内部服务器错误", "code": "INTERNAL_ERROR"}
            )
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # 取第一个IP（最原始的客户端IP）
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 回退到连接IP
        return request.client.host if request.client else "unknown"
    
    async def check_network_access(self, request: Request, client_ip: str) -> bool:
        """检查网络访问权限"""
        try:
            # 检查是否为内网IP
            is_internal = self.is_internal_ip(client_ip)
            
            # 获取请求路径
            path = str(request.url.path)
            
            # 管理员接口只允许内网访问
            if path.startswith("/api/admin/") and not is_internal:
                return False
            
            # 检查外网IP白名单
            if not is_internal and settings.external_allowed_ips:
                return client_ip in settings.external_allowed_ips
            
            return True
            
        except Exception as e:
            logger.error(f"网络访问检查异常: {e}")
            return False
    
    def is_internal_ip(self, ip: str) -> bool:
        """检查是否为内网IP"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # 检查私有网络
            if ip_obj.is_private:
                return True
            
            # 检查回环地址
            if ip_obj.is_loopback:
                return True
            
            # 检查配置的内网网段
            for network in settings.internal_networks:
                if ip_obj in ipaddress.ip_network(network):
                    return True
            
            return False
            
        except ValueError:
            logger.warning(f"无效的IP地址: {ip}")
            return False
    
    async def detect_malicious_request(self, request: Request) -> bool:
        """检测恶意请求"""
        try:
            # 检查URL路径
            path = str(request.url.path)
            query = str(request.url.query) if request.url.query else ""
            
            # 检查恶意模式
            for pattern in self.compiled_patterns:
                if pattern.search(path) or pattern.search(query):
                    return True
            
            # 检查请求头
            user_agent = request.headers.get("User-Agent", "")
            if self.is_suspicious_user_agent(user_agent):
                return True
            
            # 检查请求体（如果是POST请求）
            if request.method == "POST":
                # 这里可以添加请求体检查逻辑
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"恶意请求检测异常: {e}")
            return False
    
    def is_suspicious_user_agent(self, user_agent: str) -> bool:
        """检查可疑的User-Agent"""
        suspicious_agents = [
            "sqlmap",
            "nikto",
            "nmap",
            "masscan",
            "nessus",
            "openvas",
            "w3af",
            "burp",
            "zap"
        ]
        
        user_agent_lower = user_agent.lower()
        return any(agent in user_agent_lower for agent in suspicious_agents)
    
    def add_security_headers(self, response: Response) -> Response:
        """添加安全响应头"""
        # 防止XSS攻击
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # 防止内容类型嗅探
        response.headers["X-Content-Type-Options"] = "nosniff"
        
        # 防止点击劫持
        response.headers["X-Frame-Options"] = "DENY"
        
        # 内容安全策略
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https: wss:; "
            "frame-ancestors 'none';"
        )
        
        # 强制HTTPS（生产环境）
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains; preload"
            )
        
        # 隐藏服务器信息
        response.headers["Server"] = "FileShareSystem"
        
        # 防止缓存敏感信息
        if "/api/" in str(response.headers.get("location", "")):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        
        return response
    
    def block_ip(self, ip: str, reason: str = "安全违规"):
        """封禁IP地址"""
        self.blocked_ips.add(ip)
        logger.warning(f"IP地址已被封禁: {ip} - 原因: {reason}")
    
    def unblock_ip(self, ip: str):
        """解封IP地址"""
        self.blocked_ips.discard(ip)
        logger.info(f"IP地址已解封: {ip}")
    
    def get_blocked_ips(self) -> Set[str]:
        """获取被封禁的IP列表"""
        return self.blocked_ips.copy()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>界面原型设计 - 企业级文件共享系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f0f2f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .mockup-section {
            background: white;
            margin-bottom: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .mockup-header {
            background: #667eea;
            color: white;
            padding: 15px 25px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .mockup-content {
            padding: 25px;
        }
        
        .browser-mockup {
            border: 3px solid #ddd;
            border-radius: 8px;
            background: white;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .browser-header {
            background: #f5f5f5;
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            border-radius: 5px 5px 0 0;
        }
        
        .browser-controls {
            display: flex;
            gap: 8px;
            margin-right: 15px;
        }
        
        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .close { background: #ff5f56; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #27ca3f; }
        
        .address-bar {
            flex: 1;
            background: white;
            border: 1px solid #ddd;
            border-radius: 15px;
            padding: 5px 15px;
            font-size: 12px;
            color: #666;
        }
        
        .app-interface {
            min-height: 500px;
            background: #fafafa;
        }
        
        .top-toolbar {
            background: white;
            border-bottom: 1px solid #e8e8e8;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .search-container {
            flex: 1;
            min-width: 300px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 2px solid #667eea;
            border-radius: 25px;
            font-size: 14px;
        }
        
        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
        }
        
        .toolbar-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .view-selector {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        .main-layout {
            display: flex;
            min-height: 450px;
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e8e8e8;
            padding: 20px;
        }
        
        .sidebar-section {
            margin-bottom: 25px;
        }
        
        .sidebar-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .sidebar-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }
        
        .sidebar-item:hover {
            background: #f0f2ff;
            color: #667eea;
        }
        
        .sidebar-item.active {
            background: #667eea;
            color: white;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
            background: #fafafa;
        }
        
        .breadcrumb {
            margin-bottom: 20px;
            font-size: 13px;
            color: #666;
        }
        
        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .file-item {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .file-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }
        
        .file-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .file-icon.jpg { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .file-icon.psd { background: linear-gradient(135deg, #4834d4, #686de0); }
        .file-icon.png { background: linear-gradient(135deg, #00d2d3, #54a0ff); }
        .file-icon.ai { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
        
        .file-name {
            font-size: 12px;
            color: #333;
            word-break: break-all;
            line-height: 1.3;
        }
        
        .file-size {
            font-size: 10px;
            color: #999;
            margin-top: 5px;
        }
        
        .file-checkbox {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 16px;
            height: 16px;
        }
        
        .status-bar {
            background: white;
            border-top: 1px solid #e8e8e8;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            color: #666;
        }
        
        .pagination {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        
        .page-btn {
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal {
            background: white;
            border-radius: 10px;
            padding: 25px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-left: 4px solid #667eea;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1001;
            max-width: 350px;
        }
        
        .notification.success {
            border-left-color: #27ae60;
        }
        
        .notification.error {
            border-left-color: #e74c3c;
        }
        
        .notification.warning {
            border-left-color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 界面原型设计</h1>
            <p>企业级文件共享管理系统 - UI/UX设计方案</p>
        </div>

        <!-- 登录界面 -->
        <div class="mockup-section">
            <div class="mockup-header">
                🔐 登录界面设计
            </div>
            <div class="mockup-content">
                <div class="browser-mockup">
                    <div class="browser-header">
                        <div class="browser-controls">
                            <div class="control-btn close"></div>
                            <div class="control-btn minimize"></div>
                            <div class="control-btn maximize"></div>
                        </div>
                        <div class="address-bar">https://fileserver.company.com/login</div>
                    </div>
                    <div class="app-interface" style="display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); width: 400px;">
                            <div style="text-align: center; margin-bottom: 30px;">
                                <div style="width: 80px; height: 80px; background: #667eea; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">🏢</div>
                                <h2 style="color: #333; margin-bottom: 5px;">企业文件共享系统</h2>
                                <p style="color: #666; font-size: 14px;">安全 · 高效 · 智能</p>
                            </div>

                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" placeholder="请输入用户名" value="admin">
                            </div>

                            <div class="form-group">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-input" placeholder="请输入密码" value="••••••••">
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                                <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                                    <input type="checkbox" checked> 记住登录状态
                                </label>
                                <a href="#" style="color: #667eea; text-decoration: none; font-size: 14px;">忘记密码？</a>
                            </div>

                            <button class="btn" style="width: 100%; padding: 12px; font-size: 16px; margin-bottom: 15px;">登录</button>

                            <div style="text-align: center; font-size: 12px; color: #999;">
                                <p>首次使用？请联系管理员开通账户</p>
                                <p style="margin-top: 10px;">© 2024 企业文件共享系统 v1.0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #f8f9ff; border-radius: 8px;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">设计要点：</h4>
                    <ul style="color: #666; font-size: 14px; line-height: 1.6;">
                        <li>简洁现代的登录界面，突出企业品牌形象</li>
                        <li>渐变背景增强视觉效果，白色卡片突出主要内容</li>
                        <li>表单验证和错误提示友好易懂</li>
                        <li>支持记住登录状态和密码找回功能</li>
                        <li>响应式设计，适配不同屏幕尺寸</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主界面 -->
        <div class="mockup-section">
            <div class="mockup-header">
                🏠 主界面设计
            </div>
            <div class="mockup-content">
                <div class="browser-mockup">
                    <div class="browser-header">
                        <div class="browser-controls">
                            <div class="control-btn close"></div>
                            <div class="control-btn minimize"></div>
                            <div class="control-btn maximize"></div>
                        </div>
                        <div class="address-bar">https://fileserver.company.com/dashboard</div>
                    </div>
                    <div class="app-interface">
                        <!-- 顶部工具栏 -->
                        <div class="top-toolbar">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="font-size: 20px; font-weight: bold; color: #667eea;">📁 文件管理系统</div>
                            </div>

                            <div class="search-container">
                                <input type="text" class="search-input" placeholder="搜索文件名、标签或内容...">
                                <button class="search-btn">🔍</button>
                            </div>

                            <div class="toolbar-buttons">
                                <button class="btn">📤 上传</button>
                                <button class="btn">📥 下载</button>
                                <button class="btn btn-secondary">🔍 文本搜索</button>
                                <button class="btn btn-secondary">🖼️ 图像搜索</button>
                                <select class="view-selector">
                                    <option>🔳 大图标</option>
                                    <option>📋 详细信息</option>
                                    <option>🖼️ 缩略图</option>
                                </select>
                            </div>

                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="font-size: 14px; color: #666;">👤 admin</span>
                                <button class="btn btn-secondary" style="padding: 6px 12px;">⚙️</button>
                                <button class="btn btn-secondary" style="padding: 6px 12px;">🚪</button>
                            </div>
                        </div>

                        <!-- 主要内容区域 -->
                        <div class="main-layout">
                            <!-- 侧边栏 -->
                            <div class="sidebar">
                                <div class="sidebar-section">
                                    <div class="sidebar-title">📂 文件夹</div>
                                    <div class="sidebar-item active">🏠 首页</div>
                                    <div class="sidebar-item">🎨 设计文件</div>
                                    <div class="sidebar-item">📸 产品图片</div>
                                    <div class="sidebar-item">📄 文档资料</div>
                                    <div class="sidebar-item">🎬 视频素材</div>
                                    <div class="sidebar-item">📊 数据报表</div>
                                </div>

                                <div class="sidebar-section">
                                    <div class="sidebar-title">⚡ 快速访问</div>
                                    <div class="sidebar-item">⭐ 收藏夹</div>
                                    <div class="sidebar-item">📥 最近下载</div>
                                    <div class="sidebar-item">🔍 搜索历史</div>
                                    <div class="sidebar-item">🗑️ 回收站</div>
                                </div>

                                <div class="sidebar-section">
                                    <div class="sidebar-title">📊 统计信息</div>
                                    <div style="font-size: 12px; color: #666; padding: 8px 12px;">
                                        <div>总文件数: 1,234</div>
                                        <div>总大小: 15.6 GB</div>
                                        <div>在线用户: 8</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 内容区域 -->
                            <div class="content-area">
                                <div class="breadcrumb">
                                    <a href="#">首页</a> > <a href="#">设计文件</a> > <span>Logo设计</span>
                                </div>

                                <div class="file-grid">
                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon jpg">📷</div>
                                        <div class="file-name">company_logo_v1.jpg</div>
                                        <div class="file-size">2.3 MB</div>
                                    </div>

                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon psd">🎨</div>
                                        <div class="file-name">banner_design.psd</div>
                                        <div class="file-size">45.7 MB</div>
                                    </div>

                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon png">🖼️</div>
                                        <div class="file-name">product_shot.png</div>
                                        <div class="file-size">8.9 MB</div>
                                    </div>

                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon ai">✨</div>
                                        <div class="file-name">vector_icon.ai</div>
                                        <div class="file-size">1.2 MB</div>
                                    </div>

                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon jpg">📷</div>
                                        <div class="file-name">team_photo.jpg</div>
                                        <div class="file-size">5.4 MB</div>
                                    </div>

                                    <div class="file-item">
                                        <input type="checkbox" class="file-checkbox">
                                        <div class="file-icon psd">🎨</div>
                                        <div class="file-name">brochure_layout.psd</div>
                                        <div class="file-size">32.1 MB</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 状态栏 -->
                        <div class="status-bar">
                            <div>
                                <span>共 156 个文件</span>
                                <span style="margin-left: 20px;">已选择 0 个</span>
                                <span style="margin-left: 20px;">总大小: 2.3 GB</span>
                            </div>
                            <div class="pagination">
                                <button class="page-btn">‹</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn">2</button>
                                <button class="page-btn">3</button>
                                <button class="page-btn">›</button>
                            </div>
                            <div>
                                <span>在线用户: 8</span>
                                <span style="margin-left: 15px;">当前用户: admin</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #f8f9ff; border-radius: 8px;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">设计要点：</h4>
                    <ul style="color: #666; font-size: 14px; line-height: 1.6;">
                        <li>清晰的三栏布局：侧边栏导航 + 主内容区 + 状态信息</li>
                        <li>强大的搜索功能，支持文本和图像两种搜索模式</li>
                        <li>直观的文件图标和缩略图显示</li>
                        <li>批量操作支持，提高工作效率</li>
                        <li>实时状态显示，包括在线用户和系统统计</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 管理员界面 -->
        <div class="mockup-section">
            <div class="mockup-header">
                ⚙️ 管理员控制台
            </div>
            <div class="mockup-content">
                <div class="browser-mockup">
                    <div class="browser-header">
                        <div class="browser-controls">
                            <div class="control-btn close"></div>
                            <div class="control-btn minimize"></div>
                            <div class="control-btn maximize"></div>
                        </div>
                        <div class="address-bar">https://fileserver.company.com/admin</div>
                    </div>
                    <div class="app-interface">
                        <!-- 管理员顶部导航 -->
                        <div class="top-toolbar" style="background: #2c3e50; color: white;">
                            <div style="font-size: 18px; font-weight: bold;">🛡️ 管理员控制台</div>
                            <div style="flex: 1;"></div>
                            <div style="display: flex; gap: 20px; align-items: center;">
                                <span>🔔 3条新通知</span>
                                <span>👨‍💼 超级管理员</span>
                                <button style="background: #e74c3c; color: white; border: none; padding: 8px 15px; border-radius: 5px;">退出</button>
                            </div>
                        </div>

                        <div class="main-layout">
                            <!-- 管理员侧边栏 -->
                            <div class="sidebar" style="background: #34495e; color: white;">
                                <div class="sidebar-section">
                                    <div class="sidebar-title" style="color: #ecf0f1;">📊 系统概览</div>
                                    <div class="sidebar-item active" style="color: white;">📈 仪表板</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">📊 统计报表</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">⚠️ 系统监控</div>
                                </div>

                                <div class="sidebar-section">
                                    <div class="sidebar-title" style="color: #ecf0f1;">👥 用户管理</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">👤 用户列表</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">👥 用户组</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">🔐 权限设置</div>
                                </div>

                                <div class="sidebar-section">
                                    <div class="sidebar-title" style="color: #ecf0f1;">📁 文件管理</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">📂 共享设置</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">🗂️ 存储管理</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">🔍 搜索配置</div>
                                </div>

                                <div class="sidebar-section">
                                    <div class="sidebar-title" style="color: #ecf0f1;">🛡️ 安全中心</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">📋 访问日志</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">⚠️ 安全告警</div>
                                    <div class="sidebar-item" style="color: #bdc3c7;">🚫 黑名单</div>
                                </div>
                            </div>

                            <!-- 管理员内容区 -->
                            <div class="content-area">
                                <!-- 仪表板卡片 -->
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                                    <div style="background: white; padding: 20px; border-radius: 10px; border-left: 4px solid #3498db;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">1,234</div>
                                                <div style="color: #7f8c8d;">总文件数</div>
                                            </div>
                                            <div style="font-size: 32px; color: #3498db;">📁</div>
                                        </div>
                                    </div>

                                    <div style="background: white; padding: 20px; border-radius: 10px; border-left: 4px solid #2ecc71;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">45</div>
                                                <div style="color: #7f8c8d;">在线用户</div>
                                            </div>
                                            <div style="font-size: 32px; color: #2ecc71;">👥</div>
                                        </div>
                                    </div>

                                    <div style="background: white; padding: 20px; border-radius: 10px; border-left: 4px solid #e74c3c;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">15.6 GB</div>
                                                <div style="color: #7f8c8d;">存储使用</div>
                                            </div>
                                            <div style="font-size: 32px; color: #e74c3c;">💾</div>
                                        </div>
                                    </div>

                                    <div style="background: white; padding: 20px; border-radius: 10px; border-left: 4px solid #f39c12;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">2,567</div>
                                                <div style="color: #7f8c8d;">今日下载</div>
                                            </div>
                                            <div style="font-size: 32px; color: #f39c12;">📥</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 实时活动 -->
                                <div style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
                                    <h3 style="margin-bottom: 15px; color: #2c3e50;">📊 实时活动监控</h3>
                                    <div style="max-height: 200px; overflow-y: auto;">
                                        <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                                            <span>👤 张三 下载了 product_image.jpg</span>
                                            <span style="color: #7f8c8d;">2分钟前</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                                            <span>👤 李四 上传了 design_file.psd</span>
                                            <span style="color: #7f8c8d;">5分钟前</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                                            <span>⚠️ 检测到异常登录尝试 (IP: *************)</span>
                                            <span style="color: #e74c3c;">8分钟前</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ecf0f1;">
                                            <span>👤 王五 搜索了 "logo设计"</span>
                                            <span style="color: #7f8c8d;">12分钟前</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 系统状态 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                    <div style="background: white; border-radius: 10px; padding: 20px;">
                                        <h3 style="margin-bottom: 15px; color: #2c3e50;">🖥️ 系统状态</h3>
                                        <div style="margin-bottom: 10px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                <span>CPU使用率</span>
                                                <span>45%</span>
                                            </div>
                                            <div style="background: #ecf0f1; height: 8px; border-radius: 4px;">
                                                <div style="background: #3498db; width: 45%; height: 100%; border-radius: 4px;"></div>
                                            </div>
                                        </div>
                                        <div style="margin-bottom: 10px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                <span>内存使用率</span>
                                                <span>67%</span>
                                            </div>
                                            <div style="background: #ecf0f1; height: 8px; border-radius: 4px;">
                                                <div style="background: #f39c12; width: 67%; height: 100%; border-radius: 4px;"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                <span>磁盘使用率</span>
                                                <span>23%</span>
                                            </div>
                                            <div style="background: #ecf0f1; height: 8px; border-radius: 4px;">
                                                <div style="background: #2ecc71; width: 23%; height: 100%; border-radius: 4px;"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div style="background: white; border-radius: 10px; padding: 20px;">
                                        <h3 style="margin-bottom: 15px; color: #2c3e50;">🔔 系统通知</h3>
                                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                                            <div style="font-weight: bold; color: #856404;">⚠️ 存储空间警告</div>
                                            <div style="font-size: 12px; color: #856404;">磁盘使用率即将达到80%</div>
                                        </div>
                                        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                                            <div style="font-weight: bold; color: #0c5460;">ℹ️ 系统更新</div>
                                            <div style="font-size: 12px; color: #0c5460;">新版本v1.2.0可用</div>
                                        </div>
                                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 10px;">
                                            <div style="font-weight: bold; color: #721c24;">🚨 安全警告</div>
                                            <div style="font-size: 12px; color: #721c24;">检测到3次失败登录尝试</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #f8f9ff; border-radius: 8px;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">设计要点：</h4>
                    <ul style="color: #666; font-size: 14px; line-height: 1.6;">
                        <li>深色主题的管理员界面，突出专业性和权威性</li>
                        <li>实时数据仪表板，关键指标一目了然</li>
                        <li>活动监控和系统状态实时更新</li>
                        <li>分类清晰的功能模块，便于管理操作</li>
                        <li>重要通知和警告突出显示</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 搜索界面 -->
        <div class="mockup-section">
            <div class="mockup-header">
                🔍 智能搜索界面
            </div>
            <div class="mockup-content">
                <div class="browser-mockup">
                    <div class="browser-header">
                        <div class="browser-controls">
                            <div class="control-btn close"></div>
                            <div class="control-btn minimize"></div>
                            <div class="control-btn maximize"></div>
                        </div>
                        <div class="address-bar">https://fileserver.company.com/search</div>
                    </div>
                    <div class="app-interface">
                        <!-- 搜索页面头部 -->
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 20px; text-align: center;">
                            <h1 style="font-size: 32px; margin-bottom: 10px;">🔍 智能搜索</h1>
                            <p style="font-size: 16px; opacity: 0.9;">文本搜索 + AI图像识别，找到您需要的任何文件</p>
                        </div>

                        <!-- 搜索选项卡 -->
                        <div style="background: white; border-bottom: 1px solid #e8e8e8;">
                            <div style="display: flex; max-width: 800px; margin: 0 auto;">
                                <div style="padding: 15px 30px; border-bottom: 3px solid #667eea; color: #667eea; font-weight: bold; cursor: pointer;">📝 文本搜索</div>
                                <div style="padding: 15px 30px; color: #666; cursor: pointer;">🖼️ 图像搜索</div>
                                <div style="padding: 15px 30px; color: #666; cursor: pointer;">🔧 高级搜索</div>
                            </div>
                        </div>

                        <!-- 搜索内容区 -->
                        <div style="max-width: 800px; margin: 0 auto; padding: 30px 20px;">
                            <!-- 文本搜索 -->
                            <div style="margin-bottom: 30px;">
                                <div style="position: relative; margin-bottom: 20px;">
                                    <input type="text" style="width: 100%; padding: 15px 50px 15px 20px; border: 2px solid #667eea; border-radius: 30px; font-size: 16px;" placeholder="输入文件名、标签或关键词..." value="logo设计">
                                    <button style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); background: #667eea; color: white; border: none; border-radius: 50%; width: 40px; height: 40px; cursor: pointer;">🔍</button>
                                </div>

                                <!-- 搜索过滤器 -->
                                <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px;">
                                    <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 5px;">
                                        <option>所有类型</option>
                                        <option>图片文件</option>
                                        <option>设计文件</option>
                                        <option>文档文件</option>
                                    </select>
                                    <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 5px;">
                                        <option>所有大小</option>
                                        <option>小于1MB</option>
                                        <option>1MB-10MB</option>
                                        <option>大于10MB</option>
                                    </select>
                                    <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 5px;">
                                        <option>所有时间</option>
                                        <option>今天</option>
                                        <option>本周</option>
                                        <option>本月</option>
                                    </select>
                                    <button class="btn btn-secondary">重置</button>
                                </div>
                            </div>

                            <!-- 搜索结果 -->
                            <div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <h3>搜索结果 (找到 23 个文件)</h3>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="btn btn-secondary">📋 列表视图</button>
                                        <button class="btn">🔳 网格视图</button>
                                    </div>
                                </div>

                                <!-- 搜索结果列表 -->
                                <div style="background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #f0f0f0;">
                                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ff6b6b, #ee5a24); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; margin-right: 15px;">📷</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; margin-bottom: 5px;">company_logo_final.jpg</div>
                                            <div style="font-size: 14px; color: #666;">设计文件/Logo设计/ • 2.3 MB • 2024-01-15</div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button class="btn btn-secondary" style="padding: 6px 12px;">👁️ 预览</button>
                                            <button class="btn" style="padding: 6px 12px;">📥 下载</button>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #f0f0f0;">
                                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #4834d4, #686de0); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; margin-right: 15px;">🎨</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; margin-bottom: 5px;">logo_variations.psd</div>
                                            <div style="font-size: 14px; color: #666;">设计文件/Logo设计/ • 45.7 MB • 2024-01-12</div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button class="btn btn-secondary" style="padding: 6px 12px;">👁️ 预览</button>
                                            <button class="btn" style="padding: 6px 12px;">📥 下载</button>
                                        </div>
                                    </div>

                                    <div style="display: flex; align-items: center; padding: 15px;">
                                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ff9ff3, #f368e0); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; margin-right: 15px;">✨</div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; margin-bottom: 5px;">brand_logo_vector.ai</div>
                                            <div style="font-size: 14px; color: #666;">设计文件/品牌资料/ • 1.2 MB • 2024-01-10</div>
                                        </div>
                                        <div style="display: flex; gap: 10px;">
                                            <button class="btn btn-secondary" style="padding: 6px 12px;">👁️ 预览</button>
                                            <button class="btn" style="padding: 6px 12px;">📥 下载</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #f8f9ff; border-radius: 8px;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">设计要点：</h4>
                    <ul style="color: #666; font-size: 14px; line-height: 1.6;">
                        <li>双搜索引擎切换，支持文本和图像两种搜索模式</li>
                        <li>丰富的搜索过滤器，精确定位目标文件</li>
                        <li>搜索结果多视图展示，适应不同使用习惯</li>
                        <li>实时搜索建议和历史记录</li>
                        <li>搜索结果高亮显示匹配关键词</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

"""
用户相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from ..database import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # 用户信息
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)
    position = Column(String(100), nullable=True)
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # 安全字段
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    last_login = Column(DateTime, nullable=True)
    last_password_change = Column(DateTime, default=func.now())
    
    # MFA设置
    mfa_enabled = Column(Boolean, default=False)
    mfa_secret = Column(String(32), nullable=True)
    backup_codes = Column(JSON, nullable=True)
    
    # 网络访问设置
    allowed_ips = Column(JSON, nullable=True)  # IP白名单
    network_policy = Column(String(20), default="internal_external")  # internal_only, external_allowed, vpn_required
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    files = relationship("FileEntry", back_populates="owner", cascade="all, delete-orphan")
    activities = relationship("UserActivity", back_populates="user", cascade="all, delete-orphan")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    user_roles = relationship("UserRole", back_populates="user", cascade="all, delete-orphan")
    group_memberships = relationship("UserGroupMember", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "phone": self.phone,
            "department": self.department,
            "position": self.position,
            "is_active": self.is_active,
            "is_admin": self.is_admin,
            "is_verified": self.is_verified,
            "mfa_enabled": self.mfa_enabled,
            "network_policy": self.network_policy,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class UserGroup(Base):
    """用户组模型"""
    __tablename__ = "user_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # 组类型
    group_type = Column(String(20), default="custom")  # department, project, custom
    
    # 权限设置
    default_permissions = Column(JSON, nullable=True)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    members = relationship("UserGroupMember", back_populates="group", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<UserGroup(id={self.id}, name='{self.name}')>"


class UserGroupMember(Base):
    """用户组成员关联模型"""
    __tablename__ = "user_group_members"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    group_id = Column(Integer, ForeignKey("user_groups.id"), nullable=False)
    
    # 成员角色
    role = Column(String(20), default="member")  # admin, member
    
    # 时间字段
    joined_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 关系定义
    user = relationship("User", back_populates="group_memberships")
    group = relationship("UserGroup", back_populates="members")
    
    def __repr__(self):
        return f"<UserGroupMember(user_id={self.user_id}, group_id={self.group_id})>"


class UserSession(Base):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 会话信息
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True, index=True)
    
    # 设备信息
    device_info = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)
    
    # 地理位置信息
    location = Column(JSON, nullable=True)
    
    # 会话状态
    is_active = Column(Boolean, default=True)
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    last_activity = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id})>"
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.utcnow() > self.expires_at


class UserProfile(Base):
    """用户扩展信息模型"""
    __tablename__ = "user_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    
    # 个人信息
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="zh-CN")
    
    # 偏好设置
    preferences = Column(JSON, nullable=True)  # 用户偏好设置
    
    # 统计信息
    total_files = Column(Integer, default=0)
    total_downloads = Column(Integer, default=0)
    total_uploads = Column(Integer, default=0)
    storage_used = Column(Integer, default=0)  # 字节
    
    # 时间字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系定义
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "avatar_url": self.avatar_url,
            "bio": self.bio,
            "timezone": self.timezone,
            "language": self.language,
            "preferences": self.preferences,
            "total_files": self.total_files,
            "total_downloads": self.total_downloads,
            "total_uploads": self.total_uploads,
            "storage_used": self.storage_used,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统 - 完整开发文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .nav li {
            margin: 0;
        }
        
        .nav a {
            text-decoration: none;
            color: #667eea;
            font-weight: bold;
            padding: 12px 20px;
            border-radius: 8px;
            transition: all 0.3s;
            display: block;
            border: 2px solid transparent;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
            margin: 40px 0 25px 0;
            font-size: 2em;
        }
        
        h3 {
            color: #2c5282;
            margin: 30px 0 20px 0;
            font-size: 1.5em;
        }
        
        h4 {
            color: #4a5568;
            margin: 20px 0 15px 0;
            font-size: 1.2em;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            transition: all 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .architecture-diagram {
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            padding: 40px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid #e2e8f0;
        }
        
        .layer {
            background: white;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #667eea;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .layer:hover {
            transform: scale(1.02);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
        }
        
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 12px;
            overflow-x: auto;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            border-left: 5px solid #667eea;
            position: relative;
        }
        
        .code-block::before {
            content: "💻 代码示例";
            position: absolute;
            top: -10px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        
        th, td {
            border: 1px solid #e2e8f0;
            padding: 15px;
            text-align: left;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f8f9ff;
        }
        
        tr:hover {
            background: #e6f3ff;
        }
        
        .network-diagram {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            position: relative;
        }
        
        .network-zone {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        
        .zone-label {
            position: absolute;
            top: -12px;
            left: 20px;
            background: white;
            padding: 5px 15px;
            color: #667eea;
            font-weight: bold;
            border-radius: 15px;
        }
        
        .server-box {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px;
            text-align: center;
            display: inline-block;
            min-width: 120px;
        }
        
        .firewall-box {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin: 10px;
            text-align: center;
            display: inline-block;
            min-width: 100px;
        }
        
        .scalability-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .scalability-card {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .scalability-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
        }
        
        .permission-matrix {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            border: 2px solid #e2e8f0;
        }
        
        .timeline {
            position: relative;
            padding-left: 40px;
            margin: 30px 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 40px;
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -47px;
            top: 30px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #667eea;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            text-align: center;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #2d3748;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 5px solid #e53e3e;
        }
        
        .success-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2d3748;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 5px solid #48bb78;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 企业级文件共享系统</h1>
            <p style="font-size: 1.2em; margin-top: 10px;">可扩展架构 + 内外网权限控制 + 完整开发方案</p>
            <p style="font-size: 1em; margin-top: 10px; opacity: 0.9;">Enterprise File Sharing System - Scalable & Secure</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#" onclick="showSection('overview')">🎯 系统概述</a></li>
                <li><a href="#" onclick="showSection('scalability')">🚀 可扩展性设计</a></li>
                <li><a href="#" onclick="showSection('network')">🌐 内外网权限</a></li>
                <li><a href="#" onclick="showSection('architecture')">🏗️ 系统架构</a></li>
                <li><a href="#" onclick="showSection('modules')">📦 功能模块</a></li>
                <li><a href="#" onclick="showSection('security')">🛡️ 安全设计</a></li>
                <li><a href="#" onclick="showSection('deployment')">🚀 部署方案</a></li>
                <li><a href="#" onclick="showSection('development')">💻 开发指南</a></li>
            </ul>
        </nav>

        <!-- 系统概述 -->
        <div id="overview" class="section active">
            <h2>🎯 系统概述</h2>

            <div class="highlight-box">
                <h3>🎯 项目目标</h3>
                <p>构建一个企业级的文件共享管理系统，支持内外网访问控制、高度可扩展、安全可靠的文件管理解决方案</p>
            </div>

            <h3>📋 核心需求分析</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 多级权限控制</h4>
                    <ul>
                        <li>用户级权限：只读、修改、删除、替换</li>
                        <li>文件夹级权限：不同共享目录独立权限</li>
                        <li>网络级权限：内网/外网访问控制</li>
                        <li>时间级权限：临时权限和定时权限</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔍 双搜索引擎</h4>
                    <ul>
                        <li>文本搜索：类似Everything的高速搜索</li>
                        <li>图像搜索：AI驱动的图像识别搜索</li>
                        <li>搜索引擎切换：用户可选择搜索方式</li>
                        <li>搜索权限控制：服务端可屏蔽搜索内容</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📁 文件管理</h4>
                    <ul>
                        <li>多格式支持：JPG、PSD、TIF、AI、EPS、PNG等</li>
                        <li>缩略图生成：多种尺寸视图模式</li>
                        <li>批量操作：上传、下载、打包、删除</li>
                        <li>版本控制：文件版本管理和回滚</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📊 监控统计</h4>
                    <ul>
                        <li>用户行为记录：搜索、下载、上传统计</li>
                        <li>安全监控：敏感文件访问警告</li>
                        <li>性能监控：系统资源使用情况</li>
                        <li>审计日志：完整的操作审计追踪</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🌐 网络访问</h4>
                    <ul>
                        <li>内网访问：局域网高速访问</li>
                        <li>外网访问：互联网安全访问</li>
                        <li>VPN支持：企业VPN接入</li>
                        <li>CDN加速：全球内容分发网络</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🛡️ 安全防护</h4>
                    <ul>
                        <li>数据加密：传输和存储全程加密</li>
                        <li>访问控制：多层次安全防护</li>
                        <li>异常检测：智能安全监控</li>
                        <li>备份恢复：自动备份和灾难恢复</li>
                    </ul>
                </div>
            </div>

            <h3>🎯 系统特色</h3>
            <div class="success-box">
                <h4>✨ 核心优势</h4>
                <ul>
                    <li><strong>高可扩展性</strong>：微服务架构，支持水平扩展</li>
                    <li><strong>智能权限</strong>：细粒度的内外网权限控制</li>
                    <li><strong>AI驱动</strong>：智能图像识别和内容分析</li>
                    <li><strong>企业级安全</strong>：多层次安全防护体系</li>
                    <li><strong>高性能</strong>：异步处理和智能缓存</li>
                    <li><strong>易维护</strong>：模块化设计和完善的监控</li>
                </ul>
            </div>

            <h3>📈 技术指标</h3>
            <table>
                <tr>
                    <th>性能指标</th>
                    <th>目标值</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>并发用户数</td>
                    <td>1000+</td>
                    <td>支持千级并发用户同时访问</td>
                </tr>
                <tr>
                    <td>文件搜索速度</td>
                    <td>&lt; 100ms</td>
                    <td>毫秒级文件名搜索响应</td>
                </tr>
                <tr>
                    <td>图像识别速度</td>
                    <td>&lt; 2s</td>
                    <td>秒级图像特征提取和匹配</td>
                </tr>
                <tr>
                    <td>文件上传速度</td>
                    <td>网络带宽限制</td>
                    <td>充分利用可用带宽</td>
                </tr>
                <tr>
                    <td>系统可用性</td>
                    <td>99.9%</td>
                    <td>年度停机时间不超过8.76小时</td>
                </tr>
                <tr>
                    <td>数据安全性</td>
                    <td>企业级</td>
                    <td>符合企业数据安全标准</td>
                </tr>
            </table>
        </div>

        <!-- 可扩展性设计 -->
        <div id="scalability" class="section">
            <h2>🚀 可扩展性设计</h2>

            <h3>🏗️ 微服务架构</h3>
            <div class="architecture-diagram">
                <h4>可扩展微服务架构图</h4>
                <div class="layer">
                    <strong>🌐 API网关层 (API Gateway)</strong><br>
                    负载均衡 | 路由分发 | 认证授权 | 限流控制 | 监控统计
                </div>
                <div class="layer">
                    <strong>🔧 核心业务服务 (Core Services)</strong><br>
                    用户服务 | 文件服务 | 权限服务 | 搜索服务 | 通知服务
                </div>
                <div class="layer">
                    <strong>🤖 扩展业务服务 (Extended Services)</strong><br>
                    AI识别服务 | 缩略图服务 | 转码服务 | 统计服务 | 审计服务
                </div>
                <div class="layer">
                    <strong>💾 数据存储层 (Data Layer)</strong><br>
                    PostgreSQL集群 | Redis集群 | Elasticsearch集群 | 对象存储
                </div>
                <div class="layer">
                    <strong>🔧 基础设施层 (Infrastructure)</strong><br>
                    容器编排 | 服务发现 | 配置中心 | 日志收集 | 监控告警
                </div>
            </div>

            <h3>📈 水平扩展策略</h3>
            <div class="scalability-grid">
                <div class="scalability-card">
                    <h4>🖥️ 应用层扩展</h4>
                    <p>无状态服务设计</p>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>容器化部署</li>
                        <li>自动伸缩</li>
                        <li>负载均衡</li>
                        <li>服务发现</li>
                    </ul>
                </div>

                <div class="scalability-card">
                    <h4>💾 数据层扩展</h4>
                    <p>分布式存储架构</p>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>数据库分片</li>
                        <li>读写分离</li>
                        <li>缓存集群</li>
                        <li>对象存储</li>
                    </ul>
                </div>

                <div class="scalability-card">
                    <h4>🔍 搜索层扩展</h4>
                    <p>分布式搜索集群</p>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>索引分片</li>
                        <li>搜索负载均衡</li>
                        <li>热点数据缓存</li>
                        <li>智能路由</li>
                    </ul>
                </div>

                <div class="scalability-card">
                    <h4>📁 存储层扩展</h4>
                    <p>分布式文件存储</p>
                    <ul style="text-align: left; margin-top: 15px;">
                        <li>对象存储集群</li>
                        <li>CDN加速</li>
                        <li>多副本备份</li>
                        <li>冷热数据分离</li>
                    </ul>
                </div>
            </div>

            <h3>🔧 服务拆分策略</h3>
            <div class="code-block">
# 微服务拆分原则

## 1. 用户认证服务 (Auth Service)
- 用户登录/登出
- JWT令牌管理
- 权限验证
- 会话管理

## 2. 文件管理服务 (File Service)
- 文件上传/下载
- 文件元数据管理
- 文件版本控制
- 文件操作日志

## 3. 权限管理服务 (Permission Service)
- 用户权限管理
- 角色权限分配
- 资源访问控制
- 权限继承规则

## 4. 搜索服务 (Search Service)
- 文本搜索引擎
- 图像搜索引擎
- 搜索结果排序
- 搜索统计分析

## 5. 通知服务 (Notification Service)
- 实时通知推送
- 邮件通知
- 系统公告
- 消息队列管理

## 6. 监控服务 (Monitoring Service)
- 系统性能监控
- 用户行为分析
- 安全事件监控
- 告警管理

## 7. AI服务 (AI Service)
- 图像识别
- 内容分析
- 智能标签
- 相似度计算
            </div>

            <h3>⚡ 性能优化策略</h3>
            <table>
                <tr>
                    <th>优化层面</th>
                    <th>优化策略</th>
                    <th>预期效果</th>
                    <th>实施难度</th>
                </tr>
                <tr>
                    <td>应用层</td>
                    <td>异步处理、连接池、缓存</td>
                    <td>响应时间减少50%</td>
                    <td>中等</td>
                </tr>
                <tr>
                    <td>数据库层</td>
                    <td>索引优化、分库分表、读写分离</td>
                    <td>查询性能提升3-5倍</td>
                    <td>较高</td>
                </tr>
                <tr>
                    <td>缓存层</td>
                    <td>多级缓存、热点数据预加载</td>
                    <td>缓存命中率95%+</td>
                    <td>中等</td>
                </tr>
                <tr>
                    <td>网络层</td>
                    <td>CDN、压缩、HTTP/2</td>
                    <td>传输速度提升2-3倍</td>
                    <td>较低</td>
                </tr>
                <tr>
                    <td>存储层</td>
                    <td>SSD、对象存储、数据压缩</td>
                    <td>I/O性能提升5-10倍</td>
                    <td>中等</td>
                </tr>
            </table>
        </div>

        <!-- 内外网权限控制 -->
        <div id="network" class="section">
            <h2>🌐 内外网权限控制</h2>

            <h3>🏢 网络架构设计</h3>
            <div class="network-diagram">
                <div class="network-zone">
                    <div class="zone-label">🌍 外网区域 (Internet Zone)</div>
                    <div style="text-align: center; padding: 20px;">
                        <div class="server-box">🌐 CDN节点</div>
                        <div class="server-box">🔒 WAF防护</div>
                        <div class="server-box">⚖️ 负载均衡</div>
                        <br>
                        <div class="firewall-box">🔥 外网防火墙</div>
                    </div>
                </div>

                <div class="network-zone">
                    <div class="zone-label">🏢 DMZ区域 (Demilitarized Zone)</div>
                    <div style="text-align: center; padding: 20px;">
                        <div class="server-box">🌐 Web服务器</div>
                        <div class="server-box">🔐 认证服务器</div>
                        <div class="server-box">📊 监控服务器</div>
                        <br>
                        <div class="firewall-box">🔥 内网防火墙</div>
                    </div>
                </div>

                <div class="network-zone">
                    <div class="zone-label">🏠 内网区域 (Internal Zone)</div>
                    <div style="text-align: center; padding: 20px;">
                        <div class="server-box">💾 数据库服务器</div>
                        <div class="server-box">📁 文件服务器</div>
                        <div class="server-box">🔍 搜索服务器</div>
                        <div class="server-box">🤖 AI服务器</div>
                    </div>
                </div>
            </div>

            <h3>🔐 权限控制矩阵</h3>
            <div class="permission-matrix">
                <h4>📋 网络访问权限表</h4>
                <table>
                    <tr>
                        <th>用户类型</th>
                        <th>内网访问</th>
                        <th>外网访问</th>
                        <th>VPN访问</th>
                        <th>移动端访问</th>
                        <th>API访问</th>
                    </tr>
                    <tr>
                        <td>🔧 系统管理员</td>
                        <td>✅ 完全访问</td>
                        <td>✅ 完全访问</td>
                        <td>✅ 支持</td>
                        <td>✅ 支持</td>
                        <td>✅ 完全权限</td>
                    </tr>
                    <tr>
                        <td>👨‍💼 部门管理员</td>
                        <td>✅ 部门数据</td>
                        <td>⚠️ 受限访问</td>
                        <td>✅ 支持</td>
                        <td>✅ 支持</td>
                        <td>⚠️ 受限权限</td>
                    </tr>
                    <tr>
                        <td>👤 内部员工</td>
                        <td>✅ 授权数据</td>
                        <td>⚠️ 受限访问</td>
                        <td>✅ 支持</td>
                        <td>⚠️ 受限功能</td>
                        <td>⚠️ 基础权限</td>
                    </tr>
                    <tr>
                        <td>🤝 外部合作伙伴</td>
                        <td>❌ 禁止</td>
                        <td>⚠️ 指定数据</td>
                        <td>⚠️ 临时授权</td>
                        <td>⚠️ 受限功能</td>
                        <td>⚠️ 最小权限</td>
                    </tr>
                    <tr>
                        <td>👥 临时用户</td>
                        <td>❌ 禁止</td>
                        <td>⚠️ 公开数据</td>
                        <td>❌ 禁止</td>
                        <td>⚠️ 只读访问</td>
                        <td>⚠️ 只读权限</td>
                    </tr>
                </table>
            </div>

            <h3>🛡️ 网络安全策略</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔒 外网访问控制</h4>
                    <ul>
                        <li><strong>IP白名单</strong>：限制访问来源IP</li>
                        <li><strong>地理位置限制</strong>：基于地理位置的访问控制</li>
                        <li><strong>时间窗口限制</strong>：限制访问时间段</li>
                        <li><strong>设备指纹识别</strong>：识别和管理访问设备</li>
                        <li><strong>多因素认证</strong>：强制MFA验证</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🏠 内网访问优化</h4>
                    <ul>
                        <li><strong>域认证集成</strong>：与AD/LDAP集成</li>
                        <li><strong>单点登录</strong>：SSO无缝访问</li>
                        <li><strong>网段隔离</strong>：不同部门网络隔离</li>
                        <li><strong>带宽优化</strong>：内网高速传输</li>
                        <li><strong>缓存加速</strong>：本地缓存服务器</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔐 VPN安全通道</h4>
                    <ul>
                        <li><strong>SSL VPN</strong>：基于Web的VPN访问</li>
                        <li><strong>IPSec VPN</strong>：站点到站点连接</li>
                        <li><strong>证书认证</strong>：数字证书双向认证</li>
                        <li><strong>流量加密</strong>：端到端加密传输</li>
                        <li><strong>会话监控</strong>：VPN会话实时监控</li>
                    </ul>
                </div>
            </div>

            <h3>⚙️ 权限配置实现</h3>
            <div class="code-block">
# 网络权限配置示例

## 1. 网络访问策略配置
class NetworkAccessPolicy:
    def __init__(self):
        self.internal_networks = [
            "***********/16",
            "10.0.0.0/8",
            "**********/12"
        ]
        self.dmz_networks = [
            "*************/24"
        ]
        self.external_allowed_ips = [
            # 合作伙伴IP白名单
        ]

    def check_network_access(self, user_ip, user_role, resource_level):
        """检查网络访问权限"""
        if self.is_internal_ip(user_ip):
            return self.check_internal_access(user_role, resource_level)
        elif self.is_dmz_ip(user_ip):
            return self.check_dmz_access(user_role, resource_level)
        else:
            return self.check_external_access(user_ip, user_role, resource_level)

## 2. 文件夹网络权限配置
class FolderNetworkPermission:
    INTERNAL_ONLY = "internal_only"      # 仅内网访问
    EXTERNAL_ALLOWED = "external_allowed" # 允许外网访问
    VPN_REQUIRED = "vpn_required"        # 需要VPN访问
    DMZ_ONLY = "dmz_only"               # 仅DMZ区域访问

    def __init__(self, folder_path, network_policy):
        self.folder_path = folder_path
        self.network_policy = network_policy
        self.time_restrictions = {}  # 时间限制
        self.ip_whitelist = []       # IP白名单
        self.geo_restrictions = []   # 地理位置限制

## 3. 动态权限检查
async def check_file_access_permission(
    user_id: int,
    file_id: int,
    user_ip: str,
    access_type: str
) -> bool:
    """动态检查文件访问权限"""

    # 1. 获取用户基础权限
    user_permissions = await get_user_permissions(user_id, file_id)
    if not user_permissions.get(access_type):
        return False

    # 2. 检查网络访问权限
    file_info = await get_file_info(file_id)
    folder_network_policy = file_info.folder.network_policy

    if folder_network_policy == FolderNetworkPermission.INTERNAL_ONLY:
        if not is_internal_ip(user_ip):
            return False

    elif folder_network_policy == FolderNetworkPermission.VPN_REQUIRED:
        if not is_vpn_connection(user_ip):
            return False

    # 3. 检查时间限制
    if not check_time_restrictions(file_info.folder, datetime.now()):
        return False

    # 4. 检查地理位置限制
    if not check_geo_restrictions(file_info.folder, user_ip):
        return False

    return True
            </div>

            <h3>📊 网络监控与统计</h3>
            <table>
                <tr>
                    <th>监控指标</th>
                    <th>内网访问</th>
                    <th>外网访问</th>
                    <th>VPN访问</th>
                    <th>告警阈值</th>
                </tr>
                <tr>
                    <td>并发连接数</td>
                    <td>1000</td>
                    <td>200</td>
                    <td>100</td>
                    <td>80%使用率</td>
                </tr>
                <tr>
                    <td>带宽使用率</td>
                    <td>1Gbps</td>
                    <td>100Mbps</td>
                    <td>50Mbps</td>
                    <td>90%使用率</td>
                </tr>
                <tr>
                    <td>响应时间</td>
                    <td>&lt;50ms</td>
                    <td>&lt;200ms</td>
                    <td>&lt;500ms</td>
                    <td>超过阈值2倍</td>
                </tr>
                <tr>
                    <td>错误率</td>
                    <td>&lt;0.1%</td>
                    <td>&lt;0.5%</td>
                    <td>&lt;1%</td>
                    <td>超过5%</td>
                </tr>
                <tr>
                    <td>安全事件</td>
                    <td>0</td>
                    <td>监控中</td>
                    <td>监控中</td>
                    <td>任何安全事件</td>
                </tr>
            </table>
        </div>

        <!-- 系统架构 -->
        <div id="architecture" class="section">
            <h2>🏗️ 系统架构</h2>

            <h3>🎯 整体架构设计</h3>
            <div class="architecture-diagram">
                <h4>企业级分层架构</h4>
                <div class="layer">
                    <strong>🌐 接入层 (Access Layer)</strong><br>
                    CDN加速 | WAF防护 | 负载均衡 | SSL终结 | DDoS防护
                </div>
                <div class="layer">
                    <strong>🚪 网关层 (Gateway Layer)</strong><br>
                    API网关 | 认证授权 | 限流熔断 | 协议转换 | 监控统计
                </div>
                <div class="layer">
                    <strong>🔧 业务层 (Business Layer)</strong><br>
                    用户服务 | 文件服务 | 权限服务 | 搜索服务 | 通知服务
                </div>
                <div class="layer">
                    <strong>🤖 扩展层 (Extension Layer)</strong><br>
                    AI识别 | 图像处理 | 内容转码 | 数据分析 | 第三方集成
                </div>
                <div class="layer">
                    <strong>💾 数据层 (Data Layer)</strong><br>
                    关系数据库 | 缓存数据库 | 搜索引擎 | 对象存储 | 消息队列
                </div>
                <div class="layer">
                    <strong>🔧 基础层 (Infrastructure Layer)</strong><br>
                    容器平台 | 服务网格 | 配置中心 | 日志系统 | 监控告警
                </div>
            </div>

            <h3>🔄 数据流架构</h3>
            <div class="code-block">
# 数据流处理架构

## 1. 文件上传流程
用户上传 → API网关 → 权限验证 → 文件服务 → 对象存储
    ↓
异步任务队列 → [缩略图生成, 特征提取, 内容索引, 病毒扫描]
    ↓
数据库更新 → 搜索索引更新 → 用户通知

## 2. 文件搜索流程
搜索请求 → API网关 → 权限过滤 → 搜索服务
    ↓
[文本搜索引擎, 图像搜索引擎] → 结果合并排序
    ↓
权限过滤 → 结果返回 → 搜索日志记录

## 3. 文件下载流程
下载请求 → API网关 → 权限验证 → 文件服务
    ↓
[内网直连, 外网CDN, VPN通道] → 文件传输
    ↓
下载日志 → 统计分析 → 行为监控

## 4. 权限同步流程
权限变更 → 权限服务 → 消息队列 → 各业务服务
    ↓
缓存更新 → 搜索索引更新 → 实时生效
            </div>

            <h3>🔧 技术栈选型</h3>
            <table>
                <tr>
                    <th>技术分层</th>
                    <th>技术选型</th>
                    <th>版本要求</th>
                    <th>选型理由</th>
                </tr>
                <tr>
                    <td>前端框架</td>
                    <td>React + TypeScript</td>
                    <td>18.0+ / 5.0+</td>
                    <td>现代化、类型安全、生态丰富</td>
                </tr>
                <tr>
                    <td>后端框架</td>
                    <td>FastAPI + Python</td>
                    <td>0.104+ / 3.11+</td>
                    <td>高性能、异步支持、AI生态</td>
                </tr>
                <tr>
                    <td>数据库</td>
                    <td>PostgreSQL</td>
                    <td>15+</td>
                    <td>企业级、ACID、JSON支持</td>
                </tr>
                <tr>
                    <td>缓存</td>
                    <td>Redis</td>
                    <td>7.0+</td>
                    <td>高性能、数据结构丰富</td>
                </tr>
                <tr>
                    <td>搜索引擎</td>
                    <td>Elasticsearch</td>
                    <td>8.0+</td>
                    <td>分布式、全文搜索、分析</td>
                </tr>
                <tr>
                    <td>消息队列</td>
                    <td>Redis + Celery</td>
                    <td>7.0+ / 5.3+</td>
                    <td>简单可靠、Python生态</td>
                </tr>
                <tr>
                    <td>对象存储</td>
                    <td>MinIO / AWS S3</td>
                    <td>最新版</td>
                    <td>S3兼容、可扩展</td>
                </tr>
                <tr>
                    <td>容器平台</td>
                    <td>Docker + Kubernetes</td>
                    <td>24.0+ / 1.28+</td>
                    <td>标准化、可扩展、云原生</td>
                </tr>
                <tr>
                    <td>监控系统</td>
                    <td>Prometheus + Grafana</td>
                    <td>2.45+ / 10.0+</td>
                    <td>云原生、可视化、告警</td>
                </tr>
                <tr>
                    <td>日志系统</td>
                    <td>ELK Stack</td>
                    <td>8.0+</td>
                    <td>集中化、搜索、分析</td>
                </tr>
            </table>

            <h3>🔄 服务间通信</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🌐 同步通信</h4>
                    <ul>
                        <li><strong>HTTP/REST API</strong>：标准RESTful接口</li>
                        <li><strong>GraphQL</strong>：灵活的数据查询</li>
                        <li><strong>gRPC</strong>：高性能内部通信</li>
                        <li><strong>WebSocket</strong>：实时双向通信</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📨 异步通信</h4>
                    <ul>
                        <li><strong>消息队列</strong>：Redis/RabbitMQ</li>
                        <li><strong>事件总线</strong>：发布订阅模式</li>
                        <li><strong>任务队列</strong>：Celery异步任务</li>
                        <li><strong>流处理</strong>：实时数据流</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔍 服务发现</h4>
                    <ul>
                        <li><strong>Consul</strong>：服务注册发现</li>
                        <li><strong>Kubernetes DNS</strong>：容器服务发现</li>
                        <li><strong>负载均衡</strong>：智能路由分发</li>
                        <li><strong>健康检查</strong>：服务状态监控</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div id="modules" class="section">
            <h2>📦 功能模块设计</h2>

            <h3>👤 用户管理模块</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 认证授权</h4>
                    <ul>
                        <li><strong>多种登录方式</strong>：用户名密码、LDAP、SSO</li>
                        <li><strong>多因素认证</strong>：短信、邮箱、TOTP</li>
                        <li><strong>JWT令牌</strong>：无状态认证</li>
                        <li><strong>会话管理</strong>：在线用户管理</li>
                        <li><strong>权限继承</strong>：角色权限继承</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>👥 用户组织</h4>
                    <ul>
                        <li><strong>部门管理</strong>：层级部门结构</li>
                        <li><strong>角色管理</strong>：灵活角色定义</li>
                        <li><strong>用户组</strong>：批量权限管理</li>
                        <li><strong>临时授权</strong>：时限性权限</li>
                        <li><strong>权限审计</strong>：权限变更记录</li>
                    </ul>
                </div>
            </div>

            <h3>📁 文件管理模块</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📤 文件操作</h4>
                    <ul>
                        <li><strong>批量上传</strong>：多文件并行上传</li>
                        <li><strong>断点续传</strong>：大文件上传优化</li>
                        <li><strong>文件预览</strong>：在线预览多种格式</li>
                        <li><strong>版本控制</strong>：文件版本管理</li>
                        <li><strong>回收站</strong>：删除文件恢复</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🖼️ 图像处理</h4>
                    <ul>
                        <li><strong>缩略图生成</strong>：多尺寸缩略图</li>
                        <li><strong>格式转换</strong>：图像格式转换</li>
                        <li><strong>水印添加</strong>：版权保护</li>
                        <li><strong>EXIF提取</strong>：图像元数据</li>
                        <li><strong>压缩优化</strong>：存储空间优化</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📊 文件统计</h4>
                    <ul>
                        <li><strong>存储统计</strong>：空间使用分析</li>
                        <li><strong>访问统计</strong>：文件访问热度</li>
                        <li><strong>用户统计</strong>：用户行为分析</li>
                        <li><strong>类型统计</strong>：文件类型分布</li>
                        <li><strong>趋势分析</strong>：使用趋势预测</li>
                    </ul>
                </div>
            </div>

            <h3>🔍 搜索引擎模块</h3>
            <div class="code-block">
# 双搜索引擎架构

## 1. 文本搜索引擎 (Elasticsearch)
class TextSearchEngine:
    def __init__(self):
        self.es_client = Elasticsearch()
        self.index_name = "files"

    async def index_file(self, file_info):
        """索引文件信息"""
        doc = {
            "filename": file_info.filename,
            "content": await self.extract_text_content(file_info),
            "tags": file_info.tags,
            "file_type": file_info.file_type,
            "created_at": file_info.created_at,
            "owner_id": file_info.owner_id,
            "path": file_info.path
        }
        await self.es_client.index(
            index=self.index_name,
            id=file_info.id,
            document=doc
        )

    async def search(self, query, filters=None):
        """执行文本搜索"""
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": ["filename^3", "content^2", "tags^1.5"],
                                "fuzziness": "AUTO"
                            }
                        }
                    ],
                    "filter": filters or []
                }
            },
            "highlight": {
                "fields": {
                    "filename": {},
                    "content": {"fragment_size": 150}
                }
            }
        }
        return await self.es_client.search(
            index=self.index_name,
            body=search_body
        )

## 2. 图像搜索引擎 (AI驱动)
class ImageSearchEngine:
    def __init__(self):
        self.feature_extractor = self.load_model()
        self.vector_db = VectorDatabase()

    def load_model(self):
        """加载预训练模型"""
        return tf.keras.applications.ResNet50(
            weights='imagenet',
            include_top=False,
            pooling='avg'
        )

    async def extract_features(self, image_path):
        """提取图像特征"""
        img = cv2.imread(image_path)
        img = cv2.resize(img, (224, 224))
        img = np.expand_dims(img, axis=0)
        img = tf.keras.applications.resnet50.preprocess_input(img)

        features = self.feature_extractor.predict(img)
        return features.flatten()

    async def search_similar(self, query_image, threshold=0.8):
        """搜索相似图像"""
        query_features = await self.extract_features(query_image)
        similar_images = await self.vector_db.search_similar(
            query_features,
            threshold
        )
        return similar_images

## 3. 统一搜索接口
class UnifiedSearchService:
    def __init__(self):
        self.text_engine = TextSearchEngine()
        self.image_engine = ImageSearchEngine()

    async def search(self, query_type, query, filters=None):
        """统一搜索接口"""
        if query_type == "text":
            return await self.text_engine.search(query, filters)
        elif query_type == "image":
            return await self.image_engine.search_similar(query)
        elif query_type == "hybrid":
            # 混合搜索：文本+图像
            text_results = await self.text_engine.search(query.text, filters)
            image_results = await self.image_engine.search_similar(query.image)
            return self.merge_results(text_results, image_results)
            </div>

            <h3>📊 监控统计模块</h3>
            <table>
                <tr>
                    <th>监控类型</th>
                    <th>监控指标</th>
                    <th>数据来源</th>
                    <th>告警条件</th>
                </tr>
                <tr>
                    <td>系统性能</td>
                    <td>CPU、内存、磁盘、网络</td>
                    <td>系统监控代理</td>
                    <td>使用率>80%</td>
                </tr>
                <tr>
                    <td>应用性能</td>
                    <td>响应时间、吞吐量、错误率</td>
                    <td>应用日志</td>
                    <td>响应时间>2s</td>
                </tr>
                <tr>
                    <td>业务指标</td>
                    <td>用户活跃度、文件访问量</td>
                    <td>业务数据库</td>
                    <td>异常波动>50%</td>
                </tr>
                <tr>
                    <td>安全事件</td>
                    <td>登录失败、异常访问</td>
                    <td>安全日志</td>
                    <td>任何安全事件</td>
                </tr>
                <tr>
                    <td>数据质量</td>
                    <td>数据完整性、一致性</td>
                    <td>数据校验</td>
                    <td>数据异常</td>
                </tr>
            </table>
        </div>

        <!-- 安全设计 -->
        <div id="security" class="section">
            <h2>🛡️ 安全设计</h2>

            <h3>🔒 多层安全防护</h3>
            <div class="architecture-diagram">
                <h4>安全防护体系</h4>
                <div class="layer">
                    <strong>🌐 网络安全层</strong><br>
                    DDoS防护 | WAF防火墙 | IP白名单 | 地理位置限制 | 流量清洗
                </div>
                <div class="layer">
                    <strong>🚪 接入安全层</strong><br>
                    SSL/TLS加密 | 证书认证 | 协议安全 | 传输加密 | 完整性校验
                </div>
                <div class="layer">
                    <strong>🔐 认证安全层</strong><br>
                    多因素认证 | JWT令牌 | 会话管理 | 权限控制 | 单点登录
                </div>
                <div class="layer">
                    <strong>📊 应用安全层</strong><br>
                    输入验证 | SQL注入防护 | XSS防护 | CSRF防护 | 文件类型检查
                </div>
                <div class="layer">
                    <strong>💾 数据安全层</strong><br>
                    数据加密 | 访问控制 | 审计日志 | 备份加密 | 数据脱敏
                </div>
                <div class="layer">
                    <strong>🔧 基础安全层</strong><br>
                    系统加固 | 漏洞扫描 | 安全更新 | 入侵检测 | 安全监控
                </div>
            </div>

            <h3>🔐 身份认证与授权</h3>
            <div class="code-block">
# 企业级认证授权系统

## 1. 多因素认证 (MFA)
class MultiFactorAuth:
    def __init__(self):
        self.sms_provider = SMSProvider()
        self.email_provider = EmailProvider()
        self.totp_provider = TOTPProvider()

    async def verify_mfa(self, user_id, mfa_type, code):
        """验证多因素认证"""
        user = await get_user(user_id)

        if mfa_type == "sms":
            return await self.sms_provider.verify(user.phone, code)
        elif mfa_type == "email":
            return await self.email_provider.verify(user.email, code)
        elif mfa_type == "totp":
            return await self.totp_provider.verify(user.totp_secret, code)

        return False

## 2. 基于角色的访问控制 (RBAC)
class RBACManager:
    def __init__(self):
        self.permissions = {
            "file.read": "读取文件",
            "file.write": "写入文件",
            "file.delete": "删除文件",
            "file.download": "下载文件",
            "file.upload": "上传文件",
            "admin.user": "用户管理",
            "admin.system": "系统管理"
        }

    async def check_permission(self, user_id, resource, action):
        """检查用户权限"""
        user_roles = await self.get_user_roles(user_id)
        required_permission = f"{resource}.{action}"

        for role in user_roles:
            role_permissions = await self.get_role_permissions(role.id)
            if required_permission in role_permissions:
                return True

        return False

    async def check_resource_permission(self, user_id, resource_id, action):
        """检查资源级权限"""
        # 1. 检查基础权限
        if not await self.check_permission(user_id, "file", action):
            return False

        # 2. 检查资源所有权
        resource = await get_resource(resource_id)
        if resource.owner_id == user_id:
            return True

        # 3. 检查共享权限
        shared_permissions = await get_shared_permissions(user_id, resource_id)
        return action in shared_permissions

## 3. 动态权限控制
class DynamicPermissionManager:
    def __init__(self):
        self.time_based_rules = {}
        self.location_based_rules = {}
        self.context_based_rules = {}

    async def evaluate_permission(self, user_id, resource_id, action, context):
        """动态权限评估"""
        # 基础权限检查
        if not await self.check_base_permission(user_id, resource_id, action):
            return False

        # 时间限制检查
        if not await self.check_time_restrictions(user_id, resource_id, context.timestamp):
            return False

        # 地理位置限制检查
        if not await self.check_location_restrictions(user_id, context.ip_address):
            return False

        # 设备限制检查
        if not await self.check_device_restrictions(user_id, context.device_info):
            return False

        # 网络环境检查
        if not await self.check_network_restrictions(resource_id, context.network_type):
            return False

        return True
            </div>

            <h3>🔒 数据加密保护</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 传输加密</h4>
                    <ul>
                        <li><strong>TLS 1.3</strong>：最新传输层安全协议</li>
                        <li><strong>证书管理</strong>：自动证书更新</li>
                        <li><strong>HSTS</strong>：强制HTTPS访问</li>
                        <li><strong>完整性校验</strong>：数据传输完整性</li>
                        <li><strong>前向保密</strong>：密钥前向安全</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>💾 存储加密</h4>
                    <ul>
                        <li><strong>AES-256</strong>：文件内容加密</li>
                        <li><strong>密钥管理</strong>：分层密钥管理</li>
                        <li><strong>数据库加密</strong>：敏感字段加密</li>
                        <li><strong>备份加密</strong>：备份数据加密</li>
                        <li><strong>密钥轮换</strong>：定期密钥更新</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔍 访问控制</h4>
                    <ul>
                        <li><strong>最小权限原则</strong>：最小必要权限</li>
                        <li><strong>权限分离</strong>：职责分离原则</li>
                        <li><strong>临时权限</strong>：时限性权限授予</li>
                        <li><strong>权限审计</strong>：权限使用审计</li>
                        <li><strong>自动回收</strong>：过期权限自动回收</li>
                    </ul>
                </div>
            </div>

            <h3>🚨 安全监控与响应</h3>
            <table>
                <tr>
                    <th>安全事件类型</th>
                    <th>检测方式</th>
                    <th>响应措施</th>
                    <th>恢复时间</th>
                </tr>
                <tr>
                    <td>暴力破解攻击</td>
                    <td>登录失败次数统计</td>
                    <td>IP封禁、账户锁定</td>
                    <td>立即</td>
                </tr>
                <tr>
                    <td>异常访问行为</td>
                    <td>行为模式分析</td>
                    <td>风险评估、额外验证</td>
                    <td>实时</td>
                </tr>
                <tr>
                    <td>恶意文件上传</td>
                    <td>文件类型检查、病毒扫描</td>
                    <td>文件隔离、用户通知</td>
                    <td>立即</td>
                </tr>
                <tr>
                    <td>数据泄露风险</td>
                    <td>敏感数据访问监控</td>
                    <td>访问限制、管理员通知</td>
                    <td>5分钟内</td>
                </tr>
                <tr>
                    <td>系统入侵尝试</td>
                    <td>入侵检测系统</td>
                    <td>网络隔离、取证分析</td>
                    <td>15分钟内</td>
                </tr>
            </table>
        </div>

        <!-- 部署方案 -->
        <div id="deployment" class="section">
            <h2>🚀 部署方案</h2>

            <h3>☁️ 云原生部署架构</h3>
            <div class="architecture-diagram">
                <h4>Kubernetes集群部署</h4>
                <div class="layer">
                    <strong>🌐 Ingress层</strong><br>
                    Nginx Ingress | SSL终结 | 负载均衡 | 路由规则
                </div>
                <div class="layer">
                    <strong>🔧 应用层</strong><br>
                    Web服务Pod | API服务Pod | Worker服务Pod | 定时任务Pod
                </div>
                <div class="layer">
                    <strong>💾 数据层</strong><br>
                    PostgreSQL集群 | Redis集群 | Elasticsearch集群
                </div>
                <div class="layer">
                    <strong>📁 存储层</strong><br>
                    PVC持久卷 | 对象存储 | NFS共享存储
                </div>
            </div>

            <h3>🐳 容器化配置</h3>
            <div class="code-block">
# Kubernetes部署配置

## 1. Web应用部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-share-web
  namespace: file-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: file-share-web
  template:
    metadata:
      labels:
        app: file-share-web
    spec:
      containers:
      - name: web
        image: file-share:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: redis-config
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

## 2. 服务配置
apiVersion: v1
kind: Service
metadata:
  name: file-share-web-service
  namespace: file-system
spec:
  selector:
    app: file-share-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP

## 3. Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: file-share-ingress
  namespace: file-system
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - files.company.com
    secretName: file-share-tls
  rules:
  - host: files.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: file-share-web-service
            port:
              number: 80

## 4. 数据库配置
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
  namespace: file-system
spec:
  instances: 3
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
  bootstrap:
    initdb:
      database: filedb
      owner: fileuser
      secret:
        name: postgres-credentials
  storage:
    size: 100Gi
    storageClass: fast-ssd
            </div>

            <h3>📊 监控与运维</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📈 性能监控</h4>
                    <ul>
                        <li><strong>Prometheus</strong>：指标收集和存储</li>
                        <li><strong>Grafana</strong>：可视化仪表板</li>
                        <li><strong>AlertManager</strong>：告警管理</li>
                        <li><strong>Jaeger</strong>：分布式链路追踪</li>
                        <li><strong>自定义指标</strong>：业务指标监控</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📋 日志管理</h4>
                    <ul>
                        <li><strong>Fluentd</strong>：日志收集代理</li>
                        <li><strong>Elasticsearch</strong>：日志存储搜索</li>
                        <li><strong>Kibana</strong>：日志分析界面</li>
                        <li><strong>结构化日志</strong>：JSON格式日志</li>
                        <li><strong>日志轮转</strong>：自动日志清理</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔄 自动化运维</h4>
                    <ul>
                        <li><strong>GitOps</strong>：基于Git的部署</li>
                        <li><strong>ArgoCD</strong>：持续部署</li>
                        <li><strong>Helm</strong>：应用包管理</li>
                        <li><strong>自动扩缩容</strong>：HPA/VPA</li>
                        <li><strong>滚动更新</strong>：零停机部署</li>
                    </ul>
                </div>
            </div>

            <h3>🔧 环境配置</h3>
            <table>
                <tr>
                    <th>环境类型</th>
                    <th>资源配置</th>
                    <th>副本数量</th>
                    <th>存储配置</th>
                    <th>网络配置</th>
                </tr>
                <tr>
                    <td>开发环境</td>
                    <td>2C4G</td>
                    <td>1个副本</td>
                    <td>20GB SSD</td>
                    <td>内网访问</td>
                </tr>
                <tr>
                    <td>测试环境</td>
                    <td>4C8G</td>
                    <td>2个副本</td>
                    <td>50GB SSD</td>
                    <td>内网+VPN</td>
                </tr>
                <tr>
                    <td>预生产环境</td>
                    <td>8C16G</td>
                    <td>3个副本</td>
                    <td>200GB SSD</td>
                    <td>内网+外网</td>
                </tr>
                <tr>
                    <td>生产环境</td>
                    <td>16C32G</td>
                    <td>5个副本</td>
                    <td>1TB SSD</td>
                    <td>全网访问</td>
                </tr>
            </table>
        </div>

        <!-- 开发指南 -->
        <div id="development" class="section">
            <h2>💻 开发指南</h2>

            <h3>🚀 快速开始</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <h4>📋 环境准备</h4>
                    <ul>
                        <li>Python 3.11+ 开发环境</li>
                        <li>Docker & Docker Compose</li>
                        <li>Node.js 18+ (前端开发)</li>
                        <li>PostgreSQL 15+ 数据库</li>
                        <li>Redis 7.0+ 缓存服务</li>
                        <li>Elasticsearch 8.0+ 搜索引擎</li>
                    </ul>
                </div>

                <div class="timeline-item">
                    <h4>📦 项目初始化</h4>
                    <div class="code-block">
# 1. 克隆项目
git clone https://github.com/company/file-share-system.git
cd file-share-system

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r requirements.txt
npm install  # 前端依赖

# 4. 环境配置
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 5. 数据库初始化
alembic upgrade head

# 6. 启动开发服务
docker-compose -f docker-compose.dev.yml up -d
python run_dev.py
                    </div>
                </div>

                <div class="timeline-item">
                    <h4>🔧 开发工具配置</h4>
                    <ul>
                        <li><strong>IDE</strong>：推荐 VS Code + Python扩展</li>
                        <li><strong>代码格式化</strong>：Black + isort</li>
                        <li><strong>代码检查</strong>：Flake8 + mypy</li>
                        <li><strong>测试框架</strong>：pytest + pytest-asyncio</li>
                        <li><strong>API文档</strong>：FastAPI自动生成</li>
                    </ul>
                </div>

                <div class="timeline-item">
                    <h4>🧪 测试运行</h4>
                    <div class="code-block">
# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 运行端到端测试
pytest tests/e2e/

# 生成测试覆盖率报告
pytest --cov=app --cov-report=html

# 性能测试
locust -f tests/performance/locustfile.py
                    </div>
                </div>
            </div>

            <h3>📁 项目结构</h3>
            <div class="code-block">
file-share-system/
├── app/                          # 后端应用
│   ├── __init__.py
│   ├── main.py                   # FastAPI应用入口
│   ├── config.py                 # 配置管理
│   ├── database.py               # 数据库连接
│   ├── models/                   # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py              # 用户模型
│   │   ├── file.py              # 文件模型
│   │   ├── permission.py        # 权限模型
│   │   └── audit.py             # 审计模型
│   ├── routers/                  # API路由
│   │   ├── __init__.py
│   │   ├── auth.py              # 认证路由
│   │   ├── files.py             # 文件路由
│   │   ├── search.py            # 搜索路由
│   │   ├── admin.py             # 管理路由
│   │   └── monitoring.py        # 监控路由
│   ├── services/                 # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py      # 认证服务
│   │   ├── file_service.py      # 文件服务
│   │   ├── permission_service.py # 权限服务
│   │   ├── search_service.py    # 搜索服务
│   │   ├── notification_service.py # 通知服务
│   │   └── monitoring_service.py # 监控服务
│   ├── utils/                    # 工具函数
│   │   ├── __init__.py
│   │   ├── security.py          # 安全工具
│   │   ├── file_utils.py        # 文件工具
│   │   ├── image_utils.py       # 图像工具
│   │   ├── network_utils.py     # 网络工具
│   │   └── cache_utils.py       # 缓存工具
│   ├── tasks/                    # 异步任务
│   │   ├── __init__.py
│   │   ├── file_tasks.py        # 文件处理任务
│   │   ├── search_tasks.py      # 搜索索引任务
│   │   ├── notification_tasks.py # 通知任务
│   │   └── cleanup_tasks.py     # 清理任务
│   └── middleware/               # 中间件
│       ├── __init__.py
│       ├── auth_middleware.py   # 认证中间件
│       ├── cors_middleware.py   # CORS中间件
│       ├── rate_limit_middleware.py # 限流中间件
│       └── logging_middleware.py # 日志中间件
├── frontend/                     # 前端应用
│   ├── public/                   # 静态资源
│   ├── src/                      # 源代码
│   │   ├── components/           # React组件
│   │   ├── pages/               # 页面组件
│   │   ├── services/            # API服务
│   │   ├── utils/               # 工具函数
│   │   ├── hooks/               # 自定义Hooks
│   │   └── types/               # TypeScript类型
│   ├── package.json
│   └── tsconfig.json
├── tests/                        # 测试文件
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   ├── e2e/                      # 端到端测试
│   ├── performance/              # 性能测试
│   └── fixtures/                 # 测试数据
├── docker/                       # Docker配置
│   ├── Dockerfile.web           # Web应用镜像
│   ├── Dockerfile.worker        # Worker镜像
│   └── Dockerfile.nginx         # Nginx镜像
├── k8s/                         # Kubernetes配置
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   ├── deployment.yaml
│   ├── service.yaml
│   └── ingress.yaml
├── docs/                        # 文档
│   ├── api/                     # API文档
│   ├── deployment/              # 部署文档
│   └── development/             # 开发文档
├── scripts/                     # 脚本文件
│   ├── deploy.sh               # 部署脚本
│   ├── backup.sh               # 备份脚本
│   └── migrate.sh              # 迁移脚本
├── alembic/                     # 数据库迁移
├── requirements.txt             # Python依赖
├── docker-compose.yml           # 容器编排
├── docker-compose.dev.yml       # 开发环境
├── .env.example                 # 环境变量示例
├── .gitignore
├── README.md
└── LICENSE
            </div>

            <h3>🔧 开发规范</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📝 代码规范</h4>
                    <ul>
                        <li><strong>PEP 8</strong>：Python代码风格指南</li>
                        <li><strong>Type Hints</strong>：强制类型注解</li>
                        <li><strong>Docstrings</strong>：完整的函数文档</li>
                        <li><strong>命名规范</strong>：清晰的变量和函数命名</li>
                        <li><strong>模块化设计</strong>：单一职责原则</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🧪 测试规范</h4>
                    <ul>
                        <li><strong>测试覆盖率</strong>：要求90%以上</li>
                        <li><strong>单元测试</strong>：每个函数都要有测试</li>
                        <li><strong>集成测试</strong>：API接口测试</li>
                        <li><strong>性能测试</strong>：关键功能性能测试</li>
                        <li><strong>安全测试</strong>：安全漏洞扫描</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📋 提交规范</h4>
                    <ul>
                        <li><strong>Conventional Commits</strong>：标准化提交信息</li>
                        <li><strong>分支策略</strong>：Git Flow工作流</li>
                        <li><strong>代码审查</strong>：强制PR审查</li>
                        <li><strong>CI/CD</strong>：自动化构建部署</li>
                        <li><strong>版本管理</strong>：语义化版本号</li>
                    </ul>
                </div>
            </div>

            <h3>🔄 开发流程</h3>
            <div class="warning-box">
                <h4>⚠️ 重要提醒</h4>
                <ul>
                    <li>所有代码必须通过单元测试才能合并</li>
                    <li>敏感信息不得硬编码在代码中</li>
                    <li>数据库操作必须使用事务</li>
                    <li>API接口必须有权限验证</li>
                    <li>文件上传必须进行安全检查</li>
                </ul>
            </div>

            <div class="success-box">
                <h4>✅ 最佳实践</h4>
                <ul>
                    <li>使用异步编程提高性能</li>
                    <li>合理使用缓存减少数据库压力</li>
                    <li>实现优雅的错误处理</li>
                    <li>添加详细的日志记录</li>
                    <li>定期进行代码重构</li>
                </ul>
            </div>

            <h3>📚 相关资源</h3>
            <table>
                <tr>
                    <th>资源类型</th>
                    <th>名称</th>
                    <th>链接</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>API文档</td>
                    <td>FastAPI Swagger</td>
                    <td>/docs</td>
                    <td>自动生成的API文档</td>
                </tr>
                <tr>
                    <td>监控面板</td>
                    <td>Grafana Dashboard</td>
                    <td>/grafana</td>
                    <td>系统监控仪表板</td>
                </tr>
                <tr>
                    <td>日志查询</td>
                    <td>Kibana</td>
                    <td>/kibana</td>
                    <td>日志搜索和分析</td>
                </tr>
                <tr>
                    <td>任务监控</td>
                    <td>Flower</td>
                    <td>/flower</td>
                    <td>Celery任务监控</td>
                </tr>
                <tr>
                    <td>数据库管理</td>
                    <td>pgAdmin</td>
                    <td>/pgadmin</td>
                    <td>PostgreSQL管理界面</td>
                </tr>
            </table>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(link => {
                link.style.background = '';
                link.style.color = '#667eea';
                link.style.transform = '';
                link.style.boxShadow = '';
            });

            event.target.style.background = '#667eea';
            event.target.style.color = 'white';
            event.target.style.transform = 'translateY(-2px)';
            event.target.style.boxShadow = '0 5px 15px rgba(102, 126, 234, 0.3)';
        }

        // 默认显示第一个section
        document.addEventListener('DOMContentLoaded', function() {
            showSection('overview');
        });

        // 添加平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>

"""
数据库连接和会话管理
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool
import redis.asyncio as redis
from elasticsearch import AsyncElasticsearch
from typing import AsyncGenerator
import logging

from .config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy 基类
Base = declarative_base()

# 创建异步数据库引擎
engine = create_async_engine(
    settings.get_database_url(),
    echo=settings.debug,
    pool_size=settings.database_pool_size,
    max_overflow=settings.database_max_overflow,
    pool_pre_ping=True,
    pool_recycle=3600,
    # 在测试环境使用 NullPool
    poolclass=NullPool if settings.environment == "testing" else None,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖注入函数
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()


# Redis 连接
redis_client = redis.from_url(
    settings.get_redis_url(),
    encoding="utf-8",
    decode_responses=True,
    max_connections=settings.redis_max_connections,
    retry_on_timeout=True,
    socket_keepalive=True,
    socket_keepalive_options={},
)


async def get_redis() -> redis.Redis:
    """
    获取Redis客户端的依赖注入函数
    """
    return redis_client


# Elasticsearch 连接
es_client = AsyncElasticsearch(
    [settings.elasticsearch_url],
    max_retries=settings.elasticsearch_max_retries,
    retry_on_timeout=True,
    timeout=30,
    max_timeout=60,
)


async def get_elasticsearch() -> AsyncElasticsearch:
    """
    获取Elasticsearch客户端的依赖注入函数
    """
    return es_client


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal
        self.redis = redis_client
        self.elasticsearch = es_client
    
    async def create_tables(self):
        """创建数据库表"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建完成")
    
    async def drop_tables(self):
        """删除数据库表"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("数据库表删除完成")
    
    async def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            async with self.session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False
    
    async def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接检查失败: {e}")
            return False
    
    async def check_elasticsearch_connection(self) -> bool:
        """检查Elasticsearch连接"""
        try:
            await self.elasticsearch.ping()
            return True
        except Exception as e:
            logger.error(f"Elasticsearch连接检查失败: {e}")
            return False
    
    async def health_check(self) -> dict:
        """健康检查"""
        return {
            "database": await self.check_database_connection(),
            "redis": await self.check_redis_connection(),
            "elasticsearch": await self.check_elasticsearch_connection(),
        }
    
    async def close_connections(self):
        """关闭所有连接"""
        try:
            await self.engine.dispose()
            await self.redis.close()
            await self.elasticsearch.close()
            logger.info("所有数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接时出错: {e}")


# 创建数据库管理器实例
db_manager = DatabaseManager()


# 数据库初始化函数
async def init_database():
    """初始化数据库"""
    try:
        # 检查连接
        health = await db_manager.health_check()
        logger.info(f"数据库健康检查: {health}")
        
        # 创建表
        await db_manager.create_tables()
        
        # 初始化Elasticsearch索引
        await init_elasticsearch_indices()
        
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def init_elasticsearch_indices():
    """初始化Elasticsearch索引"""
    try:
        # 文件索引映射
        file_index_mapping = {
            "mappings": {
                "properties": {
                    "filename": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword"}
                        }
                    },
                    "content": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "file_type": {"type": "keyword"},
                    "file_size": {"type": "long"},
                    "file_path": {"type": "keyword"},
                    "owner_id": {"type": "integer"},
                    "tags": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "permissions": {"type": "keyword"},
                    "network_policy": {"type": "keyword"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "filename_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            }
        }
        
        # 创建文件索引
        index_name = settings.elasticsearch_index
        if not await es_client.indices.exists(index=index_name):
            await es_client.indices.create(
                index=index_name,
                body=file_index_mapping
            )
            logger.info(f"Elasticsearch索引 '{index_name}' 创建完成")
        else:
            logger.info(f"Elasticsearch索引 '{index_name}' 已存在")
            
    except Exception as e:
        logger.error(f"Elasticsearch索引初始化失败: {e}")
        raise


# 数据库事务装饰器
from functools import wraps
from typing import Callable, Any


def transactional(func: Callable) -> Callable:
    """数据库事务装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        async with AsyncSessionLocal() as session:
            try:
                # 将session注入到kwargs中
                kwargs['session'] = session
                result = await func(*args, **kwargs)
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                logger.error(f"事务执行失败: {e}")
                raise
            finally:
                await session.close()
    return wrapper


# 缓存装饰器
def cached(expire: int = 300):
    """Redis缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            try:
                # 尝试从缓存获取
                cached_result = await redis_client.get(cache_key)
                if cached_result:
                    import json
                    return json.loads(cached_result)
            except Exception as e:
                logger.warning(f"缓存读取失败: {e}")
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            try:
                # 存储到缓存
                import json
                await redis_client.setex(
                    cache_key,
                    expire,
                    json.dumps(result, default=str)
                )
            except Exception as e:
                logger.warning(f"缓存写入失败: {e}")
            
            return result
        return wrapper
    return decorator

"""
应用配置管理
"""
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="企业级文件共享系统", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # 数据库配置
    database_url: str = Field(
        default="postgresql+asyncpg://postgres:password@localhost:5432/filedb",
        description="数据库连接URL"
    )
    database_pool_size: int = Field(default=20, description="数据库连接池大小")
    database_max_overflow: int = Field(default=30, description="数据库连接池最大溢出")
    
    # Redis配置
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        description="Redis连接URL"
    )
    redis_max_connections: int = Field(default=20, description="Redis最大连接数")
    
    # Elasticsearch配置
    elasticsearch_url: str = Field(
        default="http://localhost:9200",
        description="Elasticsearch连接URL"
    )
    elasticsearch_index: str = Field(default="files", description="文件索引名称")
    elasticsearch_max_retries: int = Field(default=3, description="最大重试次数")
    
    # 安全配置
    secret_key: str = Field(
        default="your-super-secret-key-change-in-production",
        description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间(天)")
    
    # 文件配置
    upload_path: str = Field(default="/data/uploads", description="文件上传路径")
    thumbnail_path: str = Field(default="/data/thumbnails", description="缩略图路径")
    max_file_size: int = Field(default=100 * 1024 * 1024, description="最大文件大小(字节)")
    allowed_extensions: List[str] = Field(
        default=[
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp",
            ".psd", ".ai", ".eps", ".pdf", ".svg",
            ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".txt", ".md", ".csv", ".json", ".xml",
            ".zip", ".rar", ".7z", ".tar", ".gz"
        ],
        description="允许的文件扩展名"
    )
    
    # 搜索配置
    search_result_limit: int = Field(default=100, description="搜索结果限制")
    image_similarity_threshold: float = Field(default=0.8, description="图像相似度阈值")
    
    # Celery配置
    celery_broker_url: str = Field(
        default="redis://localhost:6379/0",
        description="Celery消息代理URL"
    )
    celery_result_backend: str = Field(
        default="redis://localhost:6379/0",
        description="Celery结果后端URL"
    )
    
    # 网络安全配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS允许的源"
    )
    internal_networks: List[str] = Field(
        default=["***********/16", "10.0.0.0/8", "**********/12"],
        description="内网网段"
    )
    external_allowed_ips: List[str] = Field(
        default=[],
        description="外网允许的IP白名单"
    )
    
    # 限流配置
    rate_limit_per_minute: int = Field(default=100, description="每分钟请求限制")
    rate_limit_per_hour: int = Field(default=1000, description="每小时请求限制")
    
    # 监控配置
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_port: int = Field(default=9090, description="指标端口")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # AI模型配置
    ai_model_path: str = Field(default="/models", description="AI模型路径")
    enable_gpu: bool = Field(default=False, description="启用GPU加速")
    
    # 邮件配置
    smtp_server: Optional[str] = Field(default=None, description="SMTP服务器")
    smtp_port: int = Field(default=587, description="SMTP端口")
    smtp_username: Optional[str] = Field(default=None, description="SMTP用户名")
    smtp_password: Optional[str] = Field(default=None, description="SMTP密码")
    smtp_use_tls: bool = Field(default=True, description="使用TLS")
    
    # 通知配置
    enable_notifications: bool = Field(default=True, description="启用通知")
    notification_channels: List[str] = Field(
        default=["email", "websocket"],
        description="通知渠道"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return self.database_url
    
    def get_redis_url(self) -> str:
        """获取Redis连接URL"""
        return self.redis_url
    
    def is_internal_ip(self, ip: str) -> bool:
        """检查是否为内网IP"""
        import ipaddress
        try:
            ip_obj = ipaddress.ip_address(ip)
            for network in self.internal_networks:
                if ip_obj in ipaddress.ip_network(network):
                    return True
            return False
        except ValueError:
            return False
    
    def is_allowed_file_extension(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        import os
        ext = os.path.splitext(filename)[1].lower()
        return ext in self.allowed_extensions


# 创建全局配置实例
settings = Settings()


# 开发环境配置
class DevelopmentSettings(Settings):
    debug: bool = True
    environment: str = "development"
    log_level: str = "DEBUG"


# 测试环境配置
class TestingSettings(Settings):
    environment: str = "testing"
    database_url: str = "postgresql+asyncpg://postgres:password@localhost:5432/filedb_test"
    redis_url: str = "redis://localhost:6379/1"


# 生产环境配置
class ProductionSettings(Settings):
    debug: bool = False
    environment: str = "production"
    log_level: str = "WARNING"


def get_settings() -> Settings:
    """根据环境变量获取配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "development":
        return DevelopmentSettings()
    elif env == "testing":
        return TestingSettings()
    elif env == "production":
        return ProductionSettings()
    else:
        return Settings()


# 导出配置实例
settings = get_settings()

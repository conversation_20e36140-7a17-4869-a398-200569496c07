<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统 - 完整开发提示词文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #2d3748;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
            z-index: 100;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .nav a {
            text-decoration: none;
            color: #667eea;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: block;
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid transparent;
        }
        
        .nav a:hover, .nav a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            border-color: rgba(255,255,255,0.3);
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: none;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .section.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
            margin: 40px 0 25px 0;
            font-size: 2.2em;
            position: relative;
        }
        
        h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        h3 {
            color: #4a5568;
            margin: 30px 0 20px 0;
            font-size: 1.6em;
            position: relative;
            padding-left: 20px;
        }
        
        h3::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #667eea;
        }
        
        .prompt-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .prompt-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .prompt-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }
        
        .prompt-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .prompt-content {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            border-left: 4px solid #667eea;
            position: relative;
        }
        
        .prompt-content::before {
            content: '💬 AI提示词';
            position: absolute;
            top: -10px;
            left: 15px;
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 12px;
            overflow-x: auto;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            border-left: 5px solid #48bb78;
            position: relative;
        }
        
        .code-block::before {
            content: '💻 代码示例';
            position: absolute;
            top: -10px;
            left: 20px;
            background: #48bb78;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            border: 2px solid #fc8181;
            color: #742a2a;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            position: relative;
        }
        
        .warning-box::before {
            content: '⚠️';
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        .success-box {
            background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
            border: 2px solid #68d391;
            color: #22543d;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            position: relative;
        }
        
        .success-box::before {
            content: '✅';
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        .info-box {
            background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%);
            border: 2px solid #63b3ed;
            color: #2a4365;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            position: relative;
        }
        
        .info-box::before {
            content: 'ℹ️';
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        th, td {
            border: 1px solid #e2e8f0;
            padding: 15px;
            text-align: left;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }
        
        tr:nth-child(even) {
            background: #f8f9ff;
        }
        
        tr:hover {
            background: #e6f3ff;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }
        
        .tag {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 企业级文件共享系统</h1>
            <p style="font-size: 1.3em; margin-top: 15px; color: #4a5568;">完整开发提示词文档</p>
            <p style="font-size: 1em; margin-top: 10px; color: #718096;">UI设计 + Python开发 + 系统架构 + 部署运维</p>
            <div style="margin-top: 20px;">
                <span class="tag">Python</span>
                <span class="tag">FastAPI</span>
                <span class="tag">React</span>
                <span class="tag">PostgreSQL</span>
                <span class="tag">Redis</span>
                <span class="tag">Elasticsearch</span>
                <span class="tag">Docker</span>
                <span class="tag">Kubernetes</span>
            </div>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#" onclick="showSection('overview')" class="active">📋 项目概述</a></li>
                <li><a href="#" onclick="showSection('ui-prompts')">🎨 UI设计提示词</a></li>
                <li><a href="#" onclick="showSection('backend-prompts')">⚙️ 后端开发提示词</a></li>
                <li><a href="#" onclick="showSection('frontend-prompts')">🌐 前端开发提示词</a></li>
                <li><a href="#" onclick="showSection('database-prompts')">🗄️ 数据库设计提示词</a></li>
                <li><a href="#" onclick="showSection('security-prompts')">🛡️ 安全开发提示词</a></li>
                <li><a href="#" onclick="showSection('deployment-prompts')">🚀 部署运维提示词</a></li>
                <li><a href="#" onclick="showSection('testing-prompts')">🧪 测试提示词</a></li>
            </ul>
        </nav>

        <!-- 项目概述 -->
        <div id="overview" class="section active">
            <h2>📋 项目概述</h2>

            <div class="info-box">
                <h3>🎯 项目目标</h3>
                <p>开发一个企业级文件共享管理系统，支持内外网访问控制、双搜索引擎（文本+图像）、多级权限管理、实时监控统计等功能。系统要求稳定流畅、搜索快速、数据安全，并具备良好的可扩展性。</p>
            </div>

            <h3>🏗️ 技术架构概览</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔧 后端技术栈</h4>
                    <ul>
                        <li><strong>框架</strong>：Python + FastAPI</li>
                        <li><strong>数据库</strong>：PostgreSQL + Redis</li>
                        <li><strong>搜索</strong>：Elasticsearch + OpenCV</li>
                        <li><strong>任务队列</strong>：Celery + Redis</li>
                        <li><strong>AI/ML</strong>：TensorFlow + OpenCV</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🌐 前端技术栈</h4>
                    <ul>
                        <li><strong>框架</strong>：React + TypeScript</li>
                        <li><strong>UI库</strong>：Ant Design</li>
                        <li><strong>状态管理</strong>：Redux Toolkit</li>
                        <li><strong>HTTP客户端</strong>：Axios</li>
                        <li><strong>桌面应用</strong>：Electron</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🚀 部署技术栈</h4>
                    <ul>
                        <li><strong>容器化</strong>：Docker + Docker Compose</li>
                        <li><strong>编排</strong>：Kubernetes + Helm</li>
                        <li><strong>监控</strong>：Prometheus + Grafana</li>
                        <li><strong>日志</strong>：ELK Stack</li>
                        <li><strong>CI/CD</strong>：GitLab CI/CD</li>
                    </ul>
                </div>
            </div>

            <h3>📋 核心功能需求</h3>
            <table>
                <tr>
                    <th>功能模块</th>
                    <th>核心需求</th>
                    <th>技术实现</th>
                    <th>优先级</th>
                </tr>
                <tr>
                    <td>用户认证</td>
                    <td>多因素认证、SSO、权限控制</td>
                    <td>JWT + RBAC + LDAP</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>文件管理</td>
                    <td>上传下载、预览、版本控制</td>
                    <td>FastAPI + 对象存储</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>搜索引擎</td>
                    <td>文本搜索 + 图像识别搜索</td>
                    <td>Elasticsearch + TensorFlow</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>权限控制</td>
                    <td>内外网访问、文件夹权限</td>
                    <td>动态权限评估</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>监控统计</td>
                    <td>用户行为、系统性能、安全监控</td>
                    <td>Prometheus + 自定义指标</td>
                    <td>中</td>
                </tr>
                <tr>
                    <td>通知系统</td>
                    <td>实时通知、滚动字幕</td>
                    <td>WebSocket + 消息队列</td>
                    <td>中</td>
                </tr>
            </table>

            <div class="warning-box">
                <h4>⚠️ 重要约束条件</h4>
                <ul>
                    <li><strong>数据隔离</strong>：不能对原共享文件做任何变化，系统数据存储到指定文件夹</li>
                    <li><strong>兼容性</strong>：必须兼容新版系统，长期可用</li>
                    <li><strong>性能要求</strong>：搜索速度要达到或超越Everything</li>
                    <li><strong>安全要求</strong>：软件加密、注册机制、完整审计日志</li>
                    <li><strong>原创性</strong>：不能有侵权行为，保证原创</li>
                </ul>
            </div>

            <div class="success-box">
                <h4>✅ 系统特色优势</h4>
                <ul>
                    <li><strong>双搜索引擎</strong>：文本搜索 + AI图像识别，满足不同搜索需求</li>
                    <li><strong>智能权限</strong>：内外网分离、动态权限评估、时间地理位置限制</li>
                    <li><strong>高可扩展</strong>：微服务架构、容器化部署、水平扩展</li>
                    <li><strong>企业级安全</strong>：多层安全防护、完整审计、异常检测</li>
                    <li><strong>实时监控</strong>：全方位监控、智能告警、性能优化</li>
                </ul>
            </div>
        </div>

        <!-- UI设计提示词 -->
        <div id="ui-prompts" class="section">
            <h2>🎨 UI设计提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔐 登录界面设计提示词
                </div>
                <div class="prompt-content">
请设计一个企业级文件共享系统的登录界面，要求：

## 设计要求：
1. **视觉风格**：现代化、简洁、专业的企业级界面
2. **色彩方案**：主色调使用蓝色系（#667eea），体现科技感和信任感
3. **布局结构**：居中卡片式布局，背景使用渐变色
4. **响应式设计**：适配桌面端、平板、手机等不同屏幕

## 功能需求：
- 用户名/密码登录
- 记住登录状态选项
- 忘记密码链接
- 多因素认证支持（预留）
- 登录状态提示
- 错误信息友好显示

## 技术实现：
- 使用React + TypeScript + Ant Design
- 表单验证使用Formik + Yup
- 状态管理使用Redux Toolkit
- 样式使用CSS-in-JS或Styled Components

## 安全考虑：
- 密码输入框遮罩
- 防止暴力破解（验证码）
- HTTPS强制跳转
- 会话超时处理

请提供完整的React组件代码，包括样式定义、表单验证、状态管理和API调用。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🏠 主界面布局设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的主界面，要求：

## 布局结构：
1. **顶部导航栏**：
   - Logo和系统名称
   - 全局搜索框（支持文本和图像搜索切换）
   - 用户信息和设置菜单
   - 通知中心图标

2. **侧边栏**：
   - 文件夹树形结构
   - 快速访问（收藏夹、最近访问、回收站）
   - 存储空间统计
   - 在线用户数显示

3. **主内容区**：
   - 面包屑导航
   - 工具栏（上传、下载、新建文件夹、视图切换）
   - 文件列表/网格视图
   - 批量操作支持

4. **状态栏**：
   - 文件统计信息
   - 分页控件
   - 当前用户状态

## 交互设计：
- 拖拽上传文件
- 右键菜单操作
- 快捷键支持
- 文件预览功能
- 批量选择操作

## 视图模式：
- 列表视图（详细信息）
- 网格视图（缩略图）
- 大图标视图
- 超大图标视图

请提供React组件架构设计和核心组件代码实现。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    ⚙️ 管理员控制台设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的管理员控制台界面，要求：

## 仪表板设计：
1. **关键指标卡片**：
   - 总文件数、在线用户数、存储使用量、今日下载量
   - 使用图表和图标进行可视化展示
   - 支持实时数据更新

2. **实时活动监控**：
   - 用户操作日志流
   - 系统事件通知
   - 安全警报显示
   - 异常行为检测

3. **系统状态监控**：
   - CPU、内存、磁盘使用率
   - 网络流量统计
   - 服务健康状态
   - 数据库连接状态

## 管理功能模块：
- 用户管理（增删改查、权限分配）
- 文件管理（共享设置、存储管理）
- 权限管理（角色配置、访问控制）
- 系统配置（参数设置、功能开关）
- 安全中心（日志审计、告警管理）

## 数据可视化：
- 使用Chart.js或ECharts进行图表展示
- 支持多种图表类型（折线图、柱状图、饼图）
- 实时数据更新和交互功能

## 响应式设计：
- 适配不同屏幕尺寸
- 移动端友好的管理界面
- 触摸操作优化

请提供管理员控制台的完整设计方案和React组件实现。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔍 智能搜索界面设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的智能搜索界面，要求：

## 搜索功能设计：
1. **双搜索引擎**：
   - 文本搜索：支持文件名、内容、标签搜索
   - 图像搜索：支持上传图片进行相似度搜索
   - 搜索引擎切换标签页

2. **高级搜索**：
   - 文件类型过滤
   - 文件大小范围
   - 创建时间范围
   - 所有者筛选
   - 标签筛选

3. **搜索结果展示**：
   - 列表视图和网格视图切换
   - 搜索关键词高亮显示
   - 相关度排序
   - 分页加载

## 用户体验优化：
- 搜索建议和自动补全
- 搜索历史记录
- 热门搜索推荐
- 搜索结果预览
- 快速操作按钮

## 图像搜索特色：
- 拖拽上传图片
- 相似度阈值调节
- 搜索结果相似度显示
- 图像特征可视化

## 性能优化：
- 搜索结果缓存
- 分页懒加载
- 搜索防抖处理
- 结果预加载

请提供搜索界面的完整设计和React组件实现，包括搜索逻辑和API集成。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 后端开发提示词 -->
        <div id="backend-prompts" class="section">
            <h2>⚙️ 后端开发提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🚀 FastAPI项目架构设计提示词
                </div>
                <div class="prompt-content">
请设计一个企业级文件共享系统的FastAPI后端架构，要求：

## 项目结构设计：
```
app/
├── main.py                    # FastAPI应用入口
├── config.py                  # 配置管理
├── database.py                # 数据库连接
├── models/                    # SQLAlchemy模型
├── routers/                   # API路由
├── services/                  # 业务逻辑层
├── utils/                     # 工具函数
├── middleware/                # 中间件
├── tasks/                     # Celery异步任务
└── tests/                     # 测试文件
```

## 核心功能模块：
1. **用户认证模块**：JWT认证、权限控制、会话管理
2. **文件管理模块**：上传下载、元数据管理、版本控制
3. **搜索引擎模块**：Elasticsearch集成、图像搜索
4. **权限管理模块**：RBAC、动态权限、网络访问控制
5. **监控统计模块**：用户行为、系统性能、安全监控
6. **通知系统模块**：实时通知、消息队列

## 技术要求：
- 使用FastAPI 0.104+和Python 3.11+
- 异步编程模式（async/await）
- SQLAlchemy 2.0 ORM
- Pydantic数据验证
- 依赖注入模式
- 中间件支持（CORS、认证、限流）

## 性能优化：
- 数据库连接池
- Redis缓存策略
- 异步任务队列
- API响应缓存
- 分页查询优化

## 安全设计：
- JWT令牌认证
- 密码加密存储
- SQL注入防护
- XSS/CSRF防护
- 文件类型验证

请提供完整的项目架构代码，包括main.py、配置管理、数据库连接和核心模型定义。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    📁 文件管理API设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的文件管理API，要求：

## API端点设计：
1. **文件上传**：
   - POST /api/files/upload
   - 支持单文件和批量上传
   - 断点续传功能
   - 文件类型验证
   - 病毒扫描集成

2. **文件下载**：
   - GET /api/files/download/{file_id}
   - 权限验证
   - 下载日志记录
   - 支持断点续传
   - 流式下载

3. **文件管理**：
   - GET /api/files/ - 文件列表
   - DELETE /api/files/{file_id} - 删除文件
   - PUT /api/files/{file_id} - 更新文件信息
   - POST /api/files/batch-download - 批量下载

## 功能实现要求：
- 异步文件处理
- 缩略图自动生成
- 文件去重（基于哈希）
- 版本控制支持
- 回收站功能

## 安全控制：
- 文件访问权限验证
- 上传文件大小限制
- 恶意文件检测
- 访问频率限制
- 操作审计日志

## 性能优化：
- 大文件分片上传
- 并发上传控制
- 缓存策略
- CDN集成
- 压缩传输

## 存储策略：
- 本地文件系统存储
- 对象存储集成（MinIO/AWS S3）
- 文件元数据数据库存储
- 冷热数据分离

请提供完整的文件管理API实现，包括路由定义、业务逻辑、错误处理和测试用例。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔍 双搜索引擎实现提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的双搜索引擎，要求：

## 文本搜索引擎（Elasticsearch）：
1. **索引设计**：
   - 文件名、内容、标签、元数据索引
   - 中文分词支持（IK分词器）
   - 自定义分析器配置
   - 索引模板和映射

2. **搜索功能**：
   - 全文搜索和精确匹配
   - 模糊搜索和拼写纠错
   - 多字段搜索和权重配置
   - 高亮显示和搜索建议
   - 聚合查询和统计分析

3. **性能优化**：
   - 搜索结果缓存
   - 分页查询优化
   - 索引分片策略
   - 搜索请求路由

## 图像搜索引擎（AI驱动）：
1. **特征提取**：
   - 使用ResNet50预训练模型
   - 图像特征向量提取
   - 特征降维和压缩
   - 特征向量存储

2. **相似度计算**：
   - 余弦相似度算法
   - 欧几里得距离计算
   - 相似度阈值配置
   - 批量相似度计算

3. **搜索优化**：
   - 向量数据库集成（Faiss/Milvus）
   - 近似最近邻搜索
   - 搜索结果排序
   - 缓存策略

## 统一搜索接口：
- 搜索类型自动识别
- 混合搜索结果合并
- 权限过滤集成
- 搜索统计和分析

## API设计：
- POST /api/search/text - 文本搜索
- POST /api/search/image - 图像搜索
- GET /api/search/suggestions - 搜索建议
- GET /api/search/history - 搜索历史

请提供完整的双搜索引擎实现，包括Elasticsearch配置、AI模型集成、API接口和性能优化方案。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔐 权限管理系统设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的权限管理系统，要求：

## 权限模型设计：
1. **RBAC模型**：
   - 用户(User) - 角色(Role) - 权限(Permission)
   - 角色继承和权限继承
   - 动态角色分配
   - 临时权限授予

2. **资源权限**：
   - 文件级权限（读、写、删除、下载、分享）
   - 文件夹级权限（继承和覆盖）
   - 功能级权限（上传、搜索、管理）
   - 网络级权限（内网、外网、VPN）

3. **动态权限控制**：
   - 基于时间的权限（工作时间、临时权限）
   - 基于地理位置的权限（IP地址、地理区域）
   - 基于设备的权限（设备指纹、设备类型）
   - 基于网络的权限（内网、外网、VPN）

## 内外网访问控制：
1. **网络环境识别**：
   - IP地址范围检测
   - 网络类型判断
   - VPN连接检测
   - 地理位置验证

2. **访问策略配置**：
   - 文件夹网络访问策略
   - 用户网络访问权限
   - 时间窗口限制
   - 异常访问检测

## 权限验证流程：
1. 用户身份验证
2. 基础权限检查
3. 资源权限验证
4. 网络环境验证
5. 动态条件评估
6. 审计日志记录

## API设计：
- POST /api/permissions/check - 权限检查
- GET /api/permissions/user/{user_id} - 用户权限
- POST /api/permissions/grant - 权限授予
- DELETE /api/permissions/revoke - 权限撤销

请提供完整的权限管理系统实现，包括数据模型、权限验证逻辑、网络访问控制和API接口。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 前端开发提示词 -->
        <div id="frontend-prompts" class="section">
            <h2>🌐 前端开发提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    ⚛️ React项目架构设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的React前端架构，要求：

## 项目结构设计：
```
src/
├── components/               # 通用组件
│   ├── common/              # 基础组件
│   ├── layout/              # 布局组件
│   └── business/            # 业务组件
├── pages/                   # 页面组件
│   ├── auth/               # 认证页面
│   ├── dashboard/          # 仪表板
│   ├── files/              # 文件管理
│   └── admin/              # 管理页面
├── services/               # API服务
├── store/                  # Redux状态管理
├── hooks/                  # 自定义Hooks
├── utils/                  # 工具函数
├── types/                  # TypeScript类型
├── styles/                 # 样式文件
└── assets/                 # 静态资源
```

## 技术栈要求：
- React 18 + TypeScript
- Ant Design UI组件库
- Redux Toolkit状态管理
- React Query数据获取
- React Router路由管理
- Axios HTTP客户端

## 核心功能组件：
1. **文件管理组件**：
   - 文件列表/网格视图
   - 文件上传组件（拖拽支持）
   - 文件预览组件
   - 批量操作组件

2. **搜索组件**：
   - 搜索输入框
   - 高级搜索面板
   - 搜索结果展示
   - 搜索历史

3. **权限管理组件**：
   - 用户管理表格
   - 权限配置面板
   - 角色分配组件

## 性能优化：
- 组件懒加载
- 虚拟滚动
- 图片懒加载
- 缓存策略
- Bundle分割

## 用户体验：
- 响应式设计
- 加载状态
- 错误边界
- 无障碍访问
- 国际化支持

请提供完整的React项目架构和核心组件实现。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    📁 文件管理组件设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的文件管理组件，要求：

## 文件列表组件：
1. **视图模式**：
   - 列表视图（详细信息）
   - 网格视图（缩略图）
   - 大图标视图
   - 超大图标视图

2. **功能特性**：
   - 文件排序（名称、大小、时间、类型）
   - 文件筛选（类型、大小、时间）
   - 批量选择操作
   - 右键菜单
   - 拖拽操作

3. **性能优化**：
   - 虚拟滚动（处理大量文件）
   - 图片懒加载
   - 缩略图缓存
   - 分页加载

## 文件上传组件：
1. **上传方式**：
   - 点击选择文件
   - 拖拽上传
   - 粘贴上传
   - 文件夹上传

2. **上传功能**：
   - 进度显示
   - 断点续传
   - 并发控制
   - 错误重试
   - 上传队列管理

3. **用户体验**：
   - 拖拽区域高亮
   - 上传预览
   - 实时进度
   - 成功/失败提示

## 文件预览组件：
1. **支持格式**：
   - 图片预览（JPG、PNG、GIF等）
   - 文档预览（PDF、DOC、PPT等）
   - 视频预览（MP4、AVI等）
   - 代码预览（语法高亮）

2. **预览功能**：
   - 全屏预览
   - 缩放控制
   - 旋转功能
   - 下载按钮
   - 分享功能

## 状态管理：
- 使用Redux Toolkit管理文件状态
- 文件列表状态
- 上传队列状态
- 选择状态管理

请提供完整的文件管理组件实现，包括TypeScript类型定义和样式。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔍 搜索组件设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的搜索组件，要求：

## 搜索输入组件：
1. **基础功能**：
   - 搜索关键词输入
   - 搜索建议下拉
   - 搜索历史显示
   - 清空搜索功能

2. **高级功能**：
   - 搜索类型切换（文本/图像）
   - 实时搜索建议
   - 搜索语法提示
   - 快捷键支持

3. **用户体验**：
   - 防抖处理
   - 加载状态
   - 错误提示
   - 无结果提示

## 高级搜索面板：
1. **筛选条件**：
   - 文件类型选择
   - 文件大小范围
   - 创建时间范围
   - 修改时间范围
   - 所有者筛选

2. **搜索选项**：
   - 搜索范围选择
   - 排序方式选择
   - 结果数量限制
   - 包含子文件夹

## 图像搜索组件：
1. **图片上传**：
   - 拖拽上传图片
   - 点击选择图片
   - 粘贴图片
   - 摄像头拍照

2. **搜索配置**：
   - 相似度阈值调节
   - 搜索范围选择
   - 结果数量限制

## 搜索结果组件：
1. **结果展示**：
   - 列表/网格视图切换
   - 关键词高亮
   - 相关度排序
   - 分页加载

2. **结果操作**：
   - 快速预览
   - 批量选择
   - 下载操作
   - 分享功能

## 搜索历史组件：
- 搜索历史记录
- 热门搜索推荐
- 历史记录清理
- 搜索统计

请提供完整的搜索组件实现，包括API集成和状态管理。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    📊 管理员仪表板组件提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的管理员仪表板组件，要求：

## 仪表板布局：
1. **关键指标卡片**：
   - 总文件数统计
   - 在线用户数
   - 存储使用量
   - 今日下载量
   - 系统健康状态

2. **图表组件**：
   - 文件上传趋势图
   - 用户活跃度图
   - 存储使用趋势
   - 下载统计图
   - 系统性能图

3. **实时监控**：
   - 实时活动流
   - 系统告警
   - 在线用户列表
   - 服务状态监控

## 数据可视化：
1. **图表库选择**：
   - 使用ECharts或Chart.js
   - 响应式图表设计
   - 交互式图表功能
   - 数据导出功能

2. **图表类型**：
   - 折线图（趋势分析）
   - 柱状图（对比分析）
   - 饼图（占比分析）
   - 仪表盘（实时指标）
   - 热力图（活跃度）

## 实时数据更新：
1. **WebSocket连接**：
   - 实时数据推送
   - 连接状态管理
   - 断线重连机制
   - 数据缓存策略

2. **数据更新策略**：
   - 增量数据更新
   - 定时数据刷新
   - 用户交互触发更新
   - 后台数据同步

## 管理功能组件：
1. **用户管理**：
   - 用户列表表格
   - 用户信息编辑
   - 权限分配界面
   - 批量操作功能

2. **系统配置**：
   - 配置参数设置
   - 功能开关控制
   - 系统维护模式
   - 备份恢复功能

## 响应式设计：
- 适配桌面端和移动端
- 图表自适应缩放
- 触摸友好的交互
- 侧边栏折叠功能

请提供完整的管理员仪表板实现，包括图表集成和实时数据更新。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 数据库设计提示词 -->
        <div id="database-prompts" class="section">
            <h2>🗄️ 数据库设计提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🏗️ 数据库架构设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的数据库架构，要求：

## 数据库选型：
1. **主数据库**：PostgreSQL 15+
   - 支持ACID事务
   - JSON数据类型支持
   - 全文搜索功能
   - 分区表支持
   - 读写分离配置

2. **缓存数据库**：Redis 7.0+
   - 会话存储
   - 缓存热点数据
   - 消息队列
   - 分布式锁
   - 限流计数器

3. **搜索引擎**：Elasticsearch 8.0+
   - 文件内容索引
   - 全文搜索
   - 聚合分析
   - 实时搜索建议

## 核心数据表设计：
1. **用户管理表**：
   - users（用户基本信息）
   - user_groups（用户组）
   - user_group_members（用户组成员）
   - user_sessions（用户会话）
   - user_profiles（用户扩展信息）

2. **文件管理表**：
   - file_entries（文件元数据）
   - directories（目录结构）
   - file_versions（文件版本）
   - file_thumbnails（缩略图）
   - file_shares（文件分享）

3. **权限管理表**：
   - roles（角色定义）
   - permissions（权限定义）
   - role_permissions（角色权限关联）
   - user_roles（用户角色关联）
   - resource_permissions（资源权限）

4. **监控日志表**：
   - user_activities（用户活动日志）
   - download_logs（下载记录）
   - search_logs（搜索记录）
   - security_alerts（安全告警）
   - system_logs（系统日志）

## 数据库优化：
1. **索引策略**：
   - 主键索引
   - 唯一索引
   - 复合索引
   - 部分索引
   - 表达式索引

2. **分区策略**：
   - 按时间分区（日志表）
   - 按哈希分区（用户表）
   - 按范围分区（文件表）

3. **性能优化**：
   - 连接池配置
   - 查询优化
   - 统计信息更新
   - 定期维护任务

请提供完整的数据库设计方案，包括DDL语句、索引创建和性能优化配置。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    📊 数据模型设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的SQLAlchemy数据模型，要求：

## 用户模型设计：
```python
class User(Base):
    __tablename__ = "users"

    # 基础字段
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)

    # 状态字段
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    last_login = Column(DateTime)

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)

    # 关系定义
    files = relationship("FileEntry", back_populates="owner")
    activities = relationship("UserActivity", back_populates="user")
```

## 文件模型设计：
1. **文件实体模型**：
   - 文件基本信息（名称、路径、大小、类型）
   - 文件哈希值（去重和完整性校验）
   - 文件状态（正常、删除、隔离）
   - 创建和修改时间

2. **文件版本模型**：
   - 版本号管理
   - 版本差异记录
   - 版本回滚支持
   - 版本清理策略

3. **文件权限模型**：
   - 文件级权限控制
   - 继承权限机制
   - 临时权限支持
   - 权限审计记录

## 权限模型设计：
1. **RBAC模型实现**：
   - 用户-角色-权限三层模型
   - 角色继承机制
   - 动态权限分配
   - 权限缓存策略

2. **资源权限模型**：
   - 资源类型定义
   - 操作类型定义
   - 权限策略配置
   - 权限评估引擎

## 监控模型设计：
1. **活动日志模型**：
   - 用户操作记录
   - 系统事件记录
   - 性能指标记录
   - 错误日志记录

2. **统计分析模型**：
   - 用户行为统计
   - 文件访问统计
   - 系统性能统计
   - 安全事件统计

## 数据关系设计：
- 一对多关系（用户-文件）
- 多对多关系（用户-角色）
- 自引用关系（目录结构）
- 继承关系（权限继承）

请提供完整的SQLAlchemy模型定义，包括关系配置、索引定义和数据验证。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔄 数据库迁移设计提示词
                </div>
                <div class="prompt-content">
请设计企业级文件共享系统的数据库迁移方案，要求：

## Alembic迁移配置：
1. **迁移环境设置**：
   - alembic.ini配置文件
   - env.py环境配置
   - 数据库连接配置
   - 迁移脚本模板

2. **迁移脚本管理**：
   - 版本号命名规范
   - 迁移脚本分类
   - 回滚脚本编写
   - 数据迁移处理

## 初始化迁移：
1. **基础表创建**：
   - 用户管理表
   - 文件管理表
   - 权限管理表
   - 系统配置表

2. **索引和约束**：
   - 主键约束
   - 外键约束
   - 唯一约束
   - 检查约束
   - 索引创建

3. **初始数据插入**：
   - 默认角色创建
   - 系统权限定义
   - 管理员账户
   - 系统配置项

## 版本升级迁移：
1. **表结构变更**：
   - 添加新字段
   - 修改字段类型
   - 删除废弃字段
   - 表重命名

2. **数据迁移**：
   - 数据格式转换
   - 数据清理和修复
   - 历史数据处理
   - 性能优化

## 迁移最佳实践：
1. **安全性考虑**：
   - 备份策略
   - 回滚计划
   - 测试验证
   - 分步执行

2. **性能优化**：
   - 批量操作
   - 索引管理
   - 锁定最小化
   - 监控执行

## 迁移脚本示例：
```python
def upgrade():
    # 创建用户表
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(50), nullable=False),
        sa.Column('email', sa.String(100), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username'),
        sa.UniqueConstraint('email')
    )

    # 创建索引
    op.create_index('ix_users_username', 'users', ['username'])
```

请提供完整的数据库迁移方案，包括迁移脚本、回滚策略和测试方案。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 安全开发提示词 -->
        <div id="security-prompts" class="section">
            <h2>🛡️ 安全开发提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔐 认证安全系统设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的认证安全系统，要求：

## JWT认证系统：
1. **Token设计**：
   - Access Token（短期有效）
   - Refresh Token（长期有效）
   - Token载荷设计
   - 签名算法选择（RS256/HS256）

2. **认证流程**：
   - 用户登录验证
   - Token生成和签发
   - Token验证和解析
   - Token刷新机制
   - 登出Token失效

3. **安全增强**：
   - Token黑名单机制
   - 设备指纹验证
   - IP地址绑定
   - 异地登录检测

## 多因素认证（MFA）：
1. **认证方式**：
   - 短信验证码
   - 邮箱验证码
   - TOTP（Google Authenticator）
   - 硬件Token
   - 生物识别

2. **实现方案**：
   - MFA策略配置
   - 验证码生成和验证
   - 备用验证方式
   - MFA绕过机制

## 密码安全：
1. **密码策略**：
   - 复杂度要求
   - 长度限制
   - 历史密码检查
   - 定期更换提醒

2. **密码存储**：
   - bcrypt哈希算法
   - 盐值生成
   - 慢哈希策略
   - 密码泄露检测

## 会话管理：
1. **会话安全**：
   - 会话ID生成
   - 会话超时控制
   - 并发会话限制
   - 会话固定攻击防护

2. **会话存储**：
   - Redis会话存储
   - 分布式会话同步
   - 会话数据加密
   - 会话清理机制

## API安全：
1. **访问控制**：
   - 基于角色的访问控制（RBAC）
   - 资源级权限验证
   - API限流控制
   - 请求签名验证

2. **输入验证**：
   - 参数类型验证
   - 数据长度限制
   - 特殊字符过滤
   - SQL注入防护

请提供完整的认证安全系统实现，包括JWT处理、MFA集成和会话管理。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🛡️ 数据安全保护设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的数据安全保护，要求：

## 数据加密：
1. **传输加密**：
   - TLS 1.3协议
   - 证书管理和更新
   - HSTS强制HTTPS
   - 证书透明度监控

2. **存储加密**：
   - 文件内容AES-256加密
   - 数据库字段加密
   - 密钥管理系统
   - 密钥轮换策略

3. **端到端加密**：
   - 客户端加密
   - 密钥交换协议
   - 前向保密性
   - 零知识架构

## 访问控制：
1. **权限模型**：
   - 最小权限原则
   - 权限分离原则
   - 动态权限评估
   - 权限审计追踪

2. **网络访问控制**：
   - IP白名单/黑名单
   - 地理位置限制
   - VPN访问控制
   - 网络分段隔离

## 数据完整性：
1. **完整性校验**：
   - 文件哈希验证
   - 数字签名验证
   - 时间戳服务
   - 区块链存证

2. **数据备份**：
   - 增量备份策略
   - 异地备份存储
   - 备份数据加密
   - 恢复测试验证

## 隐私保护：
1. **数据脱敏**：
   - 敏感数据识别
   - 脱敏算法选择
   - 脱敏效果验证
   - 脱敏数据管理

2. **数据分类**：
   - 数据敏感级别分类
   - 分类标签管理
   - 分类策略配置
   - 分类合规检查

## 安全监控：
1. **异常检测**：
   - 用户行为分析
   - 异常访问检测
   - 数据泄露监控
   - 恶意文件检测

2. **安全审计**：
   - 操作日志记录
   - 审计日志分析
   - 合规性检查
   - 安全事件响应

请提供完整的数据安全保护实现，包括加密算法、访问控制和监控机制。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🚨 安全监控系统设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的安全监控系统，要求：

## 实时监控：
1. **用户行为监控**：
   - 登录行为分析
   - 文件访问模式
   - 下载行为统计
   - 搜索行为分析
   - 异常操作检测

2. **系统安全监控**：
   - 入侵检测系统（IDS）
   - 恶意文件扫描
   - 网络流量分析
   - 系统漏洞扫描
   - 安全配置检查

## 威胁检测：
1. **攻击检测**：
   - 暴力破解检测
   - SQL注入检测
   - XSS攻击检测
   - CSRF攻击检测
   - DDoS攻击检测

2. **异常行为检测**：
   - 基线行为建立
   - 机器学习异常检测
   - 统计分析异常检测
   - 规则引擎检测
   - 行为模式分析

## 安全事件管理：
1. **事件分类**：
   - 安全事件等级分类
   - 事件类型定义
   - 事件影响评估
   - 事件处理优先级

2. **事件响应**：
   - 自动响应机制
   - 人工干预流程
   - 事件升级机制
   - 事件处理记录

## 告警系统：
1. **告警规则**：
   - 阈值告警
   - 趋势告警
   - 异常告警
   - 复合条件告警

2. **告警通知**：
   - 邮件通知
   - 短信通知
   - 即时消息通知
   - 电话告警
   - 移动应用推送

## 安全报告：
1. **定期报告**：
   - 日报、周报、月报
   - 安全态势报告
   - 威胁情报报告
   - 合规性报告

2. **实时仪表板**：
   - 安全状态概览
   - 威胁趋势图表
   - 事件处理状态
   - 系统健康状态

## 取证分析：
1. **日志收集**：
   - 系统日志收集
   - 应用日志收集
   - 网络日志收集
   - 安全设备日志

2. **证据保全**：
   - 日志完整性保护
   - 证据链管理
   - 时间戳服务
   - 数字签名验证

请提供完整的安全监控系统实现，包括检测算法、告警机制和响应流程。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 部署运维提示词 -->
        <div id="deployment-prompts" class="section">
            <h2>🚀 部署运维提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🐳 Docker容器化部署提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的Docker容器化部署，要求：

## Dockerfile设计：
1. **多阶段构建**：
   - 构建阶段（编译依赖）
   - 运行阶段（精简镜像）
   - 缓存优化
   - 镜像大小优化

2. **安全配置**：
   - 非root用户运行
   - 最小权限原则
   - 安全基础镜像
   - 漏洞扫描集成

## Docker Compose配置：
1. **服务编排**：
   - Web应用服务
   - 数据库服务
   - 缓存服务
   - 搜索引擎服务
   - 消息队列服务

2. **网络配置**：
   - 自定义网络
   - 服务发现
   - 端口映射
   - 网络隔离

3. **存储配置**：
   - 数据卷管理
   - 持久化存储
   - 备份策略
   - 存储性能优化

## 环境配置：
1. **开发环境**：
   - 热重载支持
   - 调试端口开放
   - 开发工具集成
   - 测试数据初始化

2. **生产环境**：
   - 性能优化配置
   - 安全加固设置
   - 监控集成
   - 日志收集配置

## 镜像管理：
1. **镜像构建**：
   - CI/CD集成
   - 自动化构建
   - 镜像标签管理
   - 镜像安全扫描

2. **镜像仓库**：
   - 私有镜像仓库
   - 镜像版本管理
   - 镜像分发策略
   - 镜像清理策略

## 示例配置：
```dockerfile
# 多阶段构建示例
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim as runtime
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY . .
RUN useradd --create-home --shell /bin/bash app
USER app
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

请提供完整的Docker容器化方案，包括Dockerfile、docker-compose.yml和部署脚本。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    ☸️ Kubernetes集群部署提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的Kubernetes集群部署，要求：

## 集群架构设计：
1. **命名空间规划**：
   - 开发环境命名空间
   - 测试环境命名空间
   - 生产环境命名空间
   - 监控系统命名空间

2. **资源配置**：
   - CPU和内存限制
   - 存储资源配置
   - 网络策略配置
   - 安全策略配置

## 应用部署：
1. **Deployment配置**：
   - 副本数量设置
   - 滚动更新策略
   - 健康检查配置
   - 资源限制设置

2. **Service配置**：
   - ClusterIP服务
   - NodePort服务
   - LoadBalancer服务
   - Ingress配置

3. **ConfigMap和Secret**：
   - 配置文件管理
   - 敏感信息管理
   - 环境变量配置
   - 证书管理

## 存储管理：
1. **持久化存储**：
   - PVC配置
   - StorageClass定义
   - 动态存储分配
   - 存储备份策略

2. **数据库部署**：
   - StatefulSet配置
   - 数据持久化
   - 主从复制配置
   - 备份恢复策略

## 网络配置：
1. **Ingress控制器**：
   - Nginx Ingress配置
   - SSL证书管理
   - 负载均衡策略
   - 路由规则配置

2. **网络策略**：
   - Pod间通信控制
   - 网络隔离策略
   - 安全组配置
   - 流量控制

## 自动扩缩容：
1. **HPA配置**：
   - CPU使用率扩缩容
   - 内存使用率扩缩容
   - 自定义指标扩缩容
   - 扩缩容策略配置

2. **VPA配置**：
   - 垂直扩缩容策略
   - 资源推荐
   - 自动调整配置

## 示例YAML：
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-share-web
  namespace: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: file-share-web
  template:
    metadata:
      labels:
        app: file-share-web
    spec:
      containers:
      - name: web
        image: file-share:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

请提供完整的Kubernetes部署方案，包括YAML配置文件和部署脚本。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    📊 监控运维系统设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的监控运维系统，要求：

## 监控架构：
1. **指标收集**：
   - Prometheus指标收集
   - 自定义指标定义
   - 指标标签设计
   - 指标存储策略

2. **日志收集**：
   - Fluentd日志收集
   - 日志格式标准化
   - 日志分类存储
   - 日志轮转策略

3. **链路追踪**：
   - Jaeger分布式追踪
   - 调用链分析
   - 性能瓶颈识别
   - 错误追踪定位

## 可视化监控：
1. **Grafana仪表板**：
   - 系统性能仪表板
   - 业务指标仪表板
   - 应用监控仪表板
   - 基础设施监控

2. **告警配置**：
   - AlertManager告警规则
   - 告警分级策略
   - 告警通知渠道
   - 告警抑制规则

## 监控指标：
1. **系统指标**：
   - CPU使用率
   - 内存使用率
   - 磁盘I/O
   - 网络流量
   - 系统负载

2. **应用指标**：
   - 请求响应时间
   - 请求成功率
   - 并发用户数
   - 错误率统计
   - 业务指标

3. **业务指标**：
   - 文件上传下载量
   - 用户活跃度
   - 搜索请求量
   - 存储使用量
   - 安全事件数

## 日志管理：
1. **ELK Stack配置**：
   - Elasticsearch集群
   - Logstash数据处理
   - Kibana可视化
   - 日志索引策略

2. **日志分析**：
   - 错误日志分析
   - 性能日志分析
   - 安全日志分析
   - 业务日志分析

## 自动化运维：
1. **CI/CD流水线**：
   - GitLab CI/CD配置
   - 自动化测试
   - 自动化部署
   - 回滚策略

2. **运维自动化**：
   - 自动扩缩容
   - 自动故障恢复
   - 自动备份
   - 自动巡检

## 性能优化：
1. **性能监控**：
   - 应用性能监控（APM）
   - 数据库性能监控
   - 缓存性能监控
   - 网络性能监控

2. **优化策略**：
   - 缓存优化
   - 数据库优化
   - 代码优化
   - 架构优化

请提供完整的监控运维系统实现，包括Prometheus配置、Grafana仪表板和告警规则。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>

        <!-- 测试提示词 -->
        <div id="testing-prompts" class="section">
            <h2>🧪 测试提示词</h2>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔬 单元测试设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的单元测试，要求：

## 测试框架配置：
1. **pytest配置**：
   - pytest.ini配置文件
   - 测试目录结构
   - 测试标记（markers）
   - 测试覆盖率配置

2. **测试环境**：
   - 测试数据库配置
   - 测试数据准备
   - Mock对象配置
   - 测试隔离策略

## 核心模块测试：
1. **用户认证测试**：
   - 登录功能测试
   - JWT令牌测试
   - 权限验证测试
   - 会话管理测试

2. **文件管理测试**：
   - 文件上传测试
   - 文件下载测试
   - 文件删除测试
   - 文件权限测试

3. **搜索功能测试**：
   - 文本搜索测试
   - 图像搜索测试
   - 搜索权限测试
   - 搜索性能测试

## 测试用例设计：
1. **正常流程测试**：
   - 成功场景测试
   - 边界值测试
   - 典型用例测试

2. **异常流程测试**：
   - 错误输入测试
   - 异常情况测试
   - 边界条件测试
   - 安全测试

## 测试数据管理：
1. **测试数据准备**：
   - Fixture数据定义
   - 测试数据生成
   - 数据清理策略
   - 数据隔离机制

2. **Mock和Stub**：
   - 外部依赖Mock
   - 数据库Mock
   - 网络请求Mock
   - 时间Mock

## 测试示例：
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.models import User
from app.services.auth_service import AuthService

client = TestClient(app)

class TestAuthService:
    def test_user_login_success(self, test_user):
        """测试用户登录成功"""
        response = client.post("/api/auth/login", json={
            "username": test_user.username,
            "password": "test_password"
        })
        assert response.status_code == 200
        assert "access_token" in response.json()

    def test_user_login_invalid_password(self, test_user):
        """测试用户登录密码错误"""
        response = client.post("/api/auth/login", json={
            "username": test_user.username,
            "password": "wrong_password"
        })
        assert response.status_code == 401
        assert "Invalid credentials" in response.json()["detail"]
```

## 测试覆盖率：
- 代码覆盖率要求90%以上
- 分支覆盖率要求85%以上
- 关键功能100%覆盖
- 覆盖率报告生成

请提供完整的单元测试实现，包括测试配置、测试用例和覆盖率报告。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🔗 集成测试设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的集成测试，要求：

## API集成测试：
1. **接口测试**：
   - RESTful API测试
   - 请求响应验证
   - 状态码验证
   - 数据格式验证

2. **业务流程测试**：
   - 用户注册登录流程
   - 文件上传下载流程
   - 权限分配流程
   - 搜索查询流程

## 数据库集成测试：
1. **数据操作测试**：
   - CRUD操作测试
   - 事务处理测试
   - 数据一致性测试
   - 并发访问测试

2. **数据迁移测试**：
   - 数据库升级测试
   - 数据迁移验证
   - 回滚测试
   - 数据完整性验证

## 外部服务集成测试：
1. **搜索引擎测试**：
   - Elasticsearch集成测试
   - 索引创建测试
   - 搜索查询测试
   - 数据同步测试

2. **缓存服务测试**：
   - Redis连接测试
   - 缓存读写测试
   - 缓存失效测试
   - 缓存一致性测试

## 安全集成测试：
1. **认证授权测试**：
   - JWT令牌验证测试
   - 权限控制测试
   - 会话管理测试
   - 多因素认证测试

2. **安全防护测试**：
   - SQL注入防护测试
   - XSS防护测试
   - CSRF防护测试
   - 文件上传安全测试

## 性能集成测试：
1. **响应时间测试**：
   - API响应时间测试
   - 数据库查询性能测试
   - 文件传输性能测试
   - 搜索性能测试

2. **并发测试**：
   - 多用户并发测试
   - 高并发场景测试
   - 资源竞争测试
   - 死锁检测测试

## 测试环境配置：
1. **测试数据库**：
   - 独立测试数据库
   - 测试数据初始化
   - 数据清理策略
   - 数据备份恢复

2. **测试服务**：
   - 测试环境部署
   - 服务依赖管理
   - 配置管理
   - 环境隔离

## 测试示例：
```python
class TestFileUploadIntegration:
    def test_file_upload_workflow(self, authenticated_client, test_file):
        """测试文件上传完整流程"""
        # 1. 上传文件
        response = authenticated_client.post(
            "/api/files/upload",
            files={"file": test_file}
        )
        assert response.status_code == 200
        file_id = response.json()["file_id"]

        # 2. 验证文件存在
        response = authenticated_client.get(f"/api/files/{file_id}")
        assert response.status_code == 200

        # 3. 验证搜索可以找到文件
        response = authenticated_client.get(
            "/api/search/text",
            params={"query": test_file.filename}
        )
        assert response.status_code == 200
        assert file_id in [f["id"] for f in response.json()["results"]]
```

请提供完整的集成测试实现，包括API测试、数据库测试和外部服务测试。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>

            <div class="prompt-card">
                <div class="prompt-title">
                    🚀 性能测试设计提示词
                </div>
                <div class="prompt-content">
请实现企业级文件共享系统的性能测试，要求：

## 性能测试工具：
1. **Locust负载测试**：
   - 用户行为模拟
   - 并发用户配置
   - 测试场景设计
   - 性能指标收集

2. **JMeter压力测试**：
   - HTTP请求测试
   - 数据库连接测试
   - 文件传输测试
   - 系统资源监控

## 测试场景设计：
1. **基准性能测试**：
   - 单用户性能基准
   - API响应时间基准
   - 数据库查询基准
   - 文件传输基准

2. **负载测试**：
   - 正常负载测试
   - 峰值负载测试
   - 持续负载测试
   - 渐增负载测试

3. **压力测试**：
   - 极限并发测试
   - 资源耗尽测试
   - 系统崩溃点测试
   - 恢复能力测试

## 关键性能指标：
1. **响应时间指标**：
   - 平均响应时间
   - 95%响应时间
   - 99%响应时间
   - 最大响应时间

2. **吞吐量指标**：
   - 每秒请求数（RPS）
   - 每秒事务数（TPS）
   - 并发用户数
   - 数据传输速率

3. **系统资源指标**：
   - CPU使用率
   - 内存使用率
   - 磁盘I/O
   - 网络带宽

## 性能测试脚本：
```python
from locust import HttpUser, task, between

class FileShareUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        """用户登录"""
        response = self.client.post("/api/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}

    @task(3)
    def browse_files(self):
        """浏览文件列表"""
        self.client.get("/api/files/", headers=self.headers)

    @task(2)
    def search_files(self):
        """搜索文件"""
        self.client.get(
            "/api/search/text",
            params={"query": "test"},
            headers=self.headers
        )

    @task(1)
    def upload_file(self):
        """上传文件"""
        with open("test_file.txt", "rb") as f:
            self.client.post(
                "/api/files/upload",
                files={"file": f},
                headers=self.headers
            )
```

## 性能优化建议：
1. **应用层优化**：
   - 异步处理优化
   - 缓存策略优化
   - 数据库查询优化
   - 代码性能优化

2. **系统层优化**：
   - 服务器配置优化
   - 网络配置优化
   - 存储配置优化
   - 负载均衡优化

## 性能监控：
1. **实时监控**：
   - 应用性能监控（APM）
   - 系统资源监控
   - 数据库性能监控
   - 网络性能监控

2. **性能分析**：
   - 性能瓶颈分析
   - 资源使用分析
   - 趋势分析
   - 容量规划

请提供完整的性能测试实现，包括测试脚本、监控配置和优化建议。
                </div>
                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            event.target.classList.add('active');
        }

        function copyToClipboard(button) {
            const promptContent = button.previousElementSibling;
            const text = promptContent.textContent;

            navigator.clipboard.writeText(text).then(function() {
                // 显示复制成功提示
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#48bb78';

                setTimeout(function() {
                    button.textContent = originalText;
                    button.style.background = '#667eea';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制');
            });
        }

        // 默认显示第一个section
        document.addEventListener('DOMContentLoaded', function() {
            showSection('overview');

            // 为所有导航链接添加点击事件
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                    showSection(sectionId);
                });
            });
        });

        // 添加平滑滚动效果
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'c' && e.target.closest('.prompt-content')) {
                const promptContent = e.target.closest('.prompt-card').querySelector('.prompt-content');
                const copyBtn = e.target.closest('.prompt-card').querySelector('.copy-btn');
                copyToClipboard(copyBtn);
            }
        });
    </script>
</body>
</html>

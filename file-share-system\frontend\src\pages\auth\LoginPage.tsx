import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Checkbox, Typography, Space, Divider, Alert } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { loginAsync, clearError } from '../../store/slices/authSlice';
import { LoginRequest } from '../../types/auth';
import './LoginPage.css';

const { Title, Text } = Typography;

interface LoginFormData {
  username: string;
  password: string;
  remember_me: boolean;
  mfa_token?: string;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const [showMFA, setShowMFA] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { loading, error, isAuthenticated } = useAppSelector((state) => state.auth);

  // 如果已登录，重定向到仪表板
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 清除错误信息
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleSubmit = async (values: LoginFormData) => {
    try {
      const loginData: LoginRequest = {
        username: values.username,
        password: values.password,
        remember_me: values.remember_me,
        mfa_token: values.mfa_token,
      };

      const result = await dispatch(loginAsync(loginData));
      
      if (loginAsync.fulfilled.match(result)) {
        navigate('/dashboard', { replace: true });
      } else if (loginAsync.rejected.match(result)) {
        // 检查是否需要MFA
        const errorMessage = result.payload as string;
        if (errorMessage.includes('多因素认证')) {
          setShowMFA(true);
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  const handleForgotPassword = () => {
    // TODO: 实现忘记密码功能
    console.log('忘记密码');
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <SafetyOutlined style={{ fontSize: '48px', color: '#667eea' }} />
            </div>
            <Title level={2} className="login-title">
              企业文件共享系统
            </Title>
            <Text type="secondary" className="login-subtitle">
              安全 · 高效 · 智能的文件管理平台
            </Text>
          </div>

          <Divider />

          {error && (
            <Alert
              message="登录失败"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => dispatch(clearError())}
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
            layout="vertical"
          >
            <Form.Item
              name="username"
              label="用户名/邮箱"
              rules={[
                { required: true, message: '请输入用户名或邮箱' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名或邮箱"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            {showMFA && (
              <Form.Item
                name="mfa_token"
                label="多因素认证码"
                rules={[
                  { required: true, message: '请输入多因素认证码' },
                  { len: 6, message: '认证码为6位数字' },
                ]}
              >
                <Input
                  placeholder="请输入6位认证码"
                  maxLength={6}
                  style={{ textAlign: 'center', fontSize: '18px', letterSpacing: '4px' }}
                />
              </Form.Item>
            )}

            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Form.Item name="remember_me" valuePropName="checked" noStyle>
                  <Checkbox>记住登录状态</Checkbox>
                </Form.Item>
                <Button type="link" onClick={handleForgotPassword} style={{ padding: 0 }}>
                  忘记密码？
                </Button>
              </Space>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
                style={{
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                }}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              © 2024 企业文件共享系统. 保留所有权利.
            </Text>
          </div>
        </Card>

        <div className="login-features">
          <div className="feature-item">
            <SafetyOutlined style={{ fontSize: '24px', color: '#667eea' }} />
            <div>
              <div className="feature-title">企业级安全</div>
              <div className="feature-desc">多层安全防护，保障数据安全</div>
            </div>
          </div>
          
          <div className="feature-item">
            <UserOutlined style={{ fontSize: '24px', color: '#667eea' }} />
            <div>
              <div className="feature-title">智能权限</div>
              <div className="feature-desc">细粒度权限控制，灵活管理</div>
            </div>
          </div>
          
          <div className="feature-item">
            <LockOutlined style={{ fontSize: '24px', color: '#667eea' }} />
            <div>
              <div className="feature-title">双搜索引擎</div>
              <div className="feature-desc">文本+图像搜索，快速定位</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;

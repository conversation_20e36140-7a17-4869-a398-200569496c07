.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 0;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 60px;
}

.login-card {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: none;
  animation: cardSlideIn 0.8s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
}

.login-logo {
  margin-bottom: 20px;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.login-title {
  margin-bottom: 8px !important;
  color: #2d3748;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: 14px;
  color: #718096;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 600;
  color: #4a5568;
}

.ant-input-affix-wrapper,
.ant-input {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.ant-input-affix-wrapper:hover,
.ant-input:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ant-input-affix-wrapper-focused,
.ant-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.ant-btn-primary:active {
  transform: translateY(0);
}

/* 功能特色展示 */
.login-features {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 300px;
  animation: featuresSlideIn 1s ease-out 0.3s both;
}

@keyframes featuresSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.feature-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    gap: 40px;
    padding: 20px;
  }
  
  .login-card {
    max-width: 100%;
    padding: 30px 20px;
  }
  
  .login-features {
    max-width: 100%;
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .feature-item {
    min-width: 250px;
    flex-shrink: 0;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 20px;
    border-radius: 16px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .feature-item {
    min-width: 200px;
    padding: 16px;
  }
}

/* 加载状态 */
.ant-btn-loading {
  pointer-events: none;
}

/* 错误提示样式 */
.ant-alert {
  border-radius: 12px;
  border: none;
}

.ant-alert-error {
  background: linear-gradient(135deg, #fee 0%, #fdd 100%);
  border-left: 4px solid #ff4d4f;
}

/* 复选框样式 */
.ant-checkbox-wrapper {
  color: #4a5568;
  font-weight: 500;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #667eea;
  border-color: #667eea;
}

/* 链接样式 */
.ant-btn-link {
  color: #667eea;
  font-weight: 500;
}

.ant-btn-link:hover {
  color: #5a67d8;
}

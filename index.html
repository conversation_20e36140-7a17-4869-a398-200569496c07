<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享管理系统 - 开发文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .nav {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .nav li {
            margin: 0 15px;
        }
        
        .nav a {
            text-decoration: none;
            color: #667eea;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
        
        h3 {
            color: #764ba2;
            margin: 20px 0 15px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .architecture-diagram {
            background: #f8f9ff;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .layer {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #667eea;
        }
        
        .mockup {
            background: #f0f0f0;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            position: relative;
        }
        
        .window-header {
            background: #667eea;
            color: white;
            padding: 10px;
            border-radius: 5px 5px 0 0;
            margin: -20px -20px 20px -20px;
            display: flex;
            align-items: center;
        }
        
        .window-controls {
            margin-left: auto;
        }
        
        .control {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 5px;
        }
        
        .close { background: #ff5f56; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #27ca3f; }
        
        .sidebar {
            width: 200px;
            background: #f8f9ff;
            border-right: 1px solid #ddd;
            padding: 15px;
            float: left;
            height: 300px;
        }
        
        .main-content {
            margin-left: 220px;
            padding: 15px;
            height: 300px;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        
        .file-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
        }
        
        .file-icon {
            width: 50px;
            height: 50px;
            background: #667eea;
            margin: 0 auto 10px;
            border-radius: 5px;
        }
        
        .search-bar {
            width: 100%;
            padding: 10px;
            border: 2px solid #667eea;
            border-radius: 25px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #764ba2;
        }
        
        .status-bar {
            background: #f8f9ff;
            padding: 10px;
            border-top: 1px solid #ddd;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            border-radius: 0 0 10px 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #667eea;
            color: white;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业级文件共享管理系统</h1>
            <p>开发文档 | 设计文档 | 界面原型</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#" onclick="showSection('overview')">项目概述</a></li>
                <li><a href="#" onclick="showSection('requirements')">需求分析</a></li>
                <li><a href="#" onclick="showSection('architecture')">系统架构</a></li>
                <li><a href="#" onclick="showSection('database')">数据库设计</a></li>
                <li><a href="#" onclick="showSection('api')">API设计</a></li>
                <li><a href="#" onclick="showSection('ui')">界面设计</a></li>
                <li><a href="#" onclick="showSection('security')">安全设计</a></li>
                <li><a href="#" onclick="showSection('deployment')">部署方案</a></li>
            </ul>
        </nav>

        <!-- 项目概述 -->
        <div id="overview" class="section active">
            <h2>项目概述</h2>

            <h3>项目背景</h3>
            <p>企业级文件共享管理系统是为解决多台电脑间文件共享、权限管理、快速搜索等需求而设计的综合性解决方案。系统支持内网和外网访问，具备强大的权限控制和安全监控功能。</p>

            <h3>核心特性</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 多级权限控制</h4>
                    <p>支持只读、修改、删除、替换等多种权限级别，可按用户、用户组、文件夹进行精细化权限设置</p>
                </div>
                <div class="feature-card">
                    <h4>🔍 双搜索引擎</h4>
                    <p>文件名高速搜索（类似Everything）+ AI图像识别搜索，满足不同搜索需求</p>
                </div>
                <div class="feature-card">
                    <h4>🖼️ 多格式支持</h4>
                    <p>支持JPG、PSD、TIF、AI、EPS、PNG等多种格式，提供缩略图预览和多种视图模式</p>
                </div>
                <div class="feature-card">
                    <h4>📊 行为监控</h4>
                    <p>完整记录用户搜索、下载、上传行为，提供统计分析和安全监控</p>
                </div>
                <div class="feature-card">
                    <h4>🌐 内外网访问</h4>
                    <p>支持内网和外网访问，可按文件夹单独配置访问权限</p>
                </div>
                <div class="feature-card">
                    <h4>🛡️ 安全防护</h4>
                    <p>数据加密、访问限制、敏感文件监控、违规用户管理等全方位安全保护</p>
                </div>
            </div>

            <h3>技术栈</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>后端框架</h4>
                    <p>ASP.NET Core 8.0<br>高性能、跨平台</p>
                </div>
                <div class="tech-item">
                    <h4>数据库</h4>
                    <p>PostgreSQL + Redis<br>关系型 + 缓存</p>
                </div>
                <div class="tech-item">
                    <h4>搜索引擎</h4>
                    <p>Lucene.NET + OpenCV<br>文本 + 图像搜索</p>
                </div>
                <div class="tech-item">
                    <h4>前端框架</h4>
                    <p>React + TypeScript<br>现代化Web界面</p>
                </div>
            </div>
        </div>

        <!-- 需求分析 -->
        <div id="requirements" class="section">
            <h2>需求分析</h2>

            <h3>功能性需求</h3>

            <h4>1. 文件管理需求</h4>
            <ul>
                <li><strong>多格式支持</strong>：JPG、PSD、TIF、AI、EPS、PNG等图像格式</li>
                <li><strong>缩略图生成</strong>：超大图标、大图标、中等图标、详情视图</li>
                <li><strong>文件预览</strong>：支持放大缩小，快速浏览</li>
                <li><strong>批量操作</strong>：单文件下载、打包下载、文件夹下载、批量多选下载</li>
                <li><strong>上传功能</strong>：支持单文件和批量上传</li>
            </ul>

            <h4>2. 权限管理需求</h4>
            <ul>
                <li><strong>用户权限</strong>：只读、修改、删除、替换、显示目录详细信息</li>
                <li><strong>文件夹权限</strong>：不同共享文件夹可设置不同权限</li>
                <li><strong>网络权限</strong>：内网/外网访问控制</li>
                <li><strong>用户分组</strong>：按等级分组管理（只读组、下载组、上传组等）</li>
            </ul>

            <h4>3. 搜索功能需求</h4>
            <ul>
                <li><strong>文件名搜索</strong>：类似Everything的高速搜索</li>
                <li><strong>图像识别搜索</strong>：AI驱动的图像内容识别</li>
                <li><strong>搜索引擎切换</strong>：用户可勾选使用的搜索引擎</li>
                <li><strong>搜索屏蔽</strong>：服务端可设置屏蔽搜索的信息</li>
            </ul>

            <h4>4. 安全监控需求</h4>
            <ul>
                <li><strong>行为记录</strong>：记录用户搜索、下载、上传行为</li>
                <li><strong>统计排行</strong>：用户行为统计和排行</li>
                <li><strong>敏感文件监控</strong>：敏感文件搜索和下载重点标注警告</li>
                <li><strong>访问限制</strong>：限流、违规禁止登录、时间限制倒计时</li>
                <li><strong>操作监控</strong>：删除、修改文件的严格记录和警示</li>
            </ul>

            <h3>非功能性需求</h3>

            <h4>1. 性能需求</h4>
            <ul>
                <li><strong>搜索速度</strong>：文件名搜索要达到或超越Everything的速度</li>
                <li><strong>图像识别</strong>：识图要快准</li>
                <li><strong>并发处理</strong>：支持多用户同时访问</li>
                <li><strong>大文件处理</strong>：支持大文件上传下载</li>
            </ul>

            <h4>2. 可用性需求</h4>
            <ul>
                <li><strong>界面简洁</strong>：快准稳简洁的用户体验</li>
                <li><strong>兼容性</strong>：兼容新版系统，长期可用</li>
                <li><strong>稳定性</strong>：系统稳定流畅运行</li>
                <li><strong>远程管理</strong>：支持远程管理和维护</li>
            </ul>

            <h4>3. 安全需求</h4>
            <ul>
                <li><strong>数据安全</strong>：传输和存储加密</li>
                <li><strong>访问控制</strong>：严格的身份认证和授权</li>
                <li><strong>数据隔离</strong>：不对原共享文件做任何变化</li>
                <li><strong>软件加密</strong>：软件本身加密保护</li>
            </ul>
        </div>

        <!-- 系统架构 -->
        <div id="architecture" class="section">
            <h2>系统架构设计</h2>

            <h3>整体架构</h3>
            <div class="architecture-diagram">
                <h4>分层架构图</h4>
                <div class="layer">
                    <strong>表现层 (Presentation Layer)</strong><br>
                    Web客户端 | 桌面客户端 | 移动客户端
                </div>
                <div class="layer">
                    <strong>API网关层 (API Gateway)</strong><br>
                    负载均衡 | 路由转发 | 认证授权 | 限流控制
                </div>
                <div class="layer">
                    <strong>业务逻辑层 (Business Logic Layer)</strong><br>
                    用户管理 | 文件管理 | 权限控制 | 搜索服务 | 监控服务
                </div>
                <div class="layer">
                    <strong>数据访问层 (Data Access Layer)</strong><br>
                    ORM映射 | 缓存管理 | 数据库连接池
                </div>
                <div class="layer">
                    <strong>数据存储层 (Data Storage Layer)</strong><br>
                    PostgreSQL | Redis | 文件系统 | 日志存储
                </div>
            </div>

            <h3>核心模块设计</h3>

            <h4>1. 用户认证与授权模块</h4>
            <ul>
                <li><strong>JWT认证</strong>：基于Token的无状态认证</li>
                <li><strong>RBAC权限模型</strong>：基于角色的访问控制</li>
                <li><strong>多级权限</strong>：用户级、组级、资源级权限控制</li>
                <li><strong>会话管理</strong>：在线用户管理和会话控制</li>
            </ul>

            <h4>2. 文件管理模块</h4>
            <ul>
                <li><strong>文件存储</strong>：原文件不变，元数据分离存储</li>
                <li><strong>缩略图服务</strong>：异步生成多尺寸缩略图</li>
                <li><strong>文件预览</strong>：支持多格式在线预览</li>
                <li><strong>批量操作</strong>：队列化处理批量任务</li>
            </ul>

            <h4>3. 搜索引擎模块</h4>
            <ul>
                <li><strong>文件名搜索</strong>：基于Lucene.NET的全文索引</li>
                <li><strong>图像搜索</strong>：基于深度学习的图像特征提取</li>
                <li><strong>搜索缓存</strong>：热门搜索结果缓存</li>
                <li><strong>搜索统计</strong>：搜索行为分析和统计</li>
            </ul>

            <h4>4. 监控与日志模块</h4>
            <ul>
                <li><strong>行为监控</strong>：用户操作实时监控</li>
                <li><strong>性能监控</strong>：系统性能指标监控</li>
                <li><strong>安全监控</strong>：异常行为检测和告警</li>
                <li><strong>日志管理</strong>：结构化日志存储和查询</li>
            </ul>

            <h3>技术选型说明</h3>

            <h4>后端技术栈</h4>
            <div class="code-block">
// 主要技术组件
ASP.NET Core 8.0          // Web框架
Entity Framework Core     // ORM框架
PostgreSQL               // 主数据库
Redis                   // 缓存数据库
Lucene.NET             // 搜索引擎
OpenCV                 // 图像处理
TensorFlow.NET         // 机器学习
SignalR                // 实时通信
Serilog                // 日志框架
AutoMapper             // 对象映射
FluentValidation       // 数据验证
            </div>

            <h4>前端技术栈</h4>
            <div class="code-block">
// 前端技术组件
React 18               // UI框架
TypeScript            // 类型安全
Ant Design            // UI组件库
Redux Toolkit         // 状态管理
React Query           // 数据获取
Axios                 // HTTP客户端
Socket.IO             // 实时通信
Electron              // 桌面应用
            </div>
        </div>

        <!-- 数据库设计 -->
        <div id="database" class="section">
            <h2>数据库设计</h2>

            <h3>数据库架构</h3>
            <p>系统采用PostgreSQL作为主数据库，Redis作为缓存数据库，确保数据的一致性和高性能访问。</p>

            <h3>核心数据表设计</h3>

            <h4>1. 用户管理相关表</h4>
            <table>
                <tr>
                    <th>表名</th>
                    <th>说明</th>
                    <th>主要字段</th>
                </tr>
                <tr>
                    <td>Users</td>
                    <td>用户基本信息</td>
                    <td>Id, Username, PasswordHash, Email, Status, CreatedAt</td>
                </tr>
                <tr>
                    <td>UserGroups</td>
                    <td>用户组信息</td>
                    <td>Id, GroupName, Description, Permissions, CreatedAt</td>
                </tr>
                <tr>
                    <td>UserGroupMembers</td>
                    <td>用户组成员关系</td>
                    <td>UserId, GroupId, JoinedAt</td>
                </tr>
                <tr>
                    <td>UserSessions</td>
                    <td>用户会话管理</td>
                    <td>Id, UserId, Token, ExpiresAt, IpAddress, UserAgent</td>
                </tr>
            </table>

            <h4>2. 文件管理相关表</h4>
            <table>
                <tr>
                    <th>表名</th>
                    <th>说明</th>
                    <th>主要字段</th>
                </tr>
                <tr>
                    <td>FileEntries</td>
                    <td>文件元数据</td>
                    <td>Id, FileName, FilePath, FileSize, FileType, Hash, CreatedAt</td>
                </tr>
                <tr>
                    <td>Directories</td>
                    <td>目录结构</td>
                    <td>Id, DirectoryName, ParentId, FullPath, IsShared</td>
                </tr>
                <tr>
                    <td>FileThumbnails</td>
                    <td>缩略图信息</td>
                    <td>FileId, ThumbnailSize, ThumbnailPath, GeneratedAt</td>
                </tr>
                <tr>
                    <td>FilePermissions</td>
                    <td>文件权限</td>
                    <td>Id, FileId, UserId, GroupId, PermissionType, GrantedAt</td>
                </tr>
            </table>

            <h4>3. 搜索相关表</h4>
            <table>
                <tr>
                    <th>表名</th>
                    <th>说明</th>
                    <th>主要字段</th>
                </tr>
                <tr>
                    <td>SearchIndexes</td>
                    <td>搜索索引</td>
                    <td>Id, FileId, IndexType, IndexContent, UpdatedAt</td>
                </tr>
                <tr>
                    <td>ImageFeatures</td>
                    <td>图像特征</td>
                    <td>FileId, FeatureVector, ExtractionMethod, CreatedAt</td>
                </tr>
                <tr>
                    <td>SearchLogs</td>
                    <td>搜索日志</td>
                    <td>Id, UserId, SearchQuery, SearchType, ResultCount, SearchTime</td>
                </tr>
            </table>

            <h4>4. 监控日志相关表</h4>
            <table>
                <tr>
                    <th>表名</th>
                    <th>说明</th>
                    <th>主要字段</th>
                </tr>
                <tr>
                    <td>UserActivities</td>
                    <td>用户活动日志</td>
                    <td>Id, UserId, ActivityType, ResourceId, Details, Timestamp</td>
                </tr>
                <tr>
                    <td>DownloadLogs</td>
                    <td>下载记录</td>
                    <td>Id, UserId, FileId, DownloadSize, DownloadTime, IpAddress</td>
                </tr>
                <tr>
                    <td>SecurityAlerts</td>
                    <td>安全告警</td>
                    <td>Id, UserId, AlertType, Description, Severity, CreatedAt</td>
                </tr>
                <tr>
                    <td>SystemStats</td>
                    <td>系统统计</td>
                    <td>Id, StatType, StatValue, RecordDate</td>
                </tr>
            </table>
        </div>

        <!-- API设计 -->
        <div id="api" class="section">
            <h2>API设计文档</h2>

            <h3>API架构设计</h3>
            <p>系统采用RESTful API设计风格，所有API遵循统一的响应格式和错误处理机制。</p>

            <h4>统一响应格式</h4>
            <div class="code-block">
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200,
  "timestamp": "2024-01-01T00:00:00Z"
}
            </div>

            <h3>核心API接口</h3>

            <h4>1. 用户认证API</h4>
            <table>
                <tr>
                    <th>接口</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>参数</th>
                </tr>
                <tr>
                    <td>/api/auth/login</td>
                    <td>POST</td>
                    <td>用户登录</td>
                    <td>username, password</td>
                </tr>
                <tr>
                    <td>/api/auth/logout</td>
                    <td>POST</td>
                    <td>用户登出</td>
                    <td>token</td>
                </tr>
                <tr>
                    <td>/api/auth/refresh</td>
                    <td>POST</td>
                    <td>刷新Token</td>
                    <td>refreshToken</td>
                </tr>
                <tr>
                    <td>/api/auth/profile</td>
                    <td>GET</td>
                    <td>获取用户信息</td>
                    <td>-</td>
                </tr>
            </table>

            <h4>2. 文件管理API</h4>
            <table>
                <tr>
                    <th>接口</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>参数</th>
                </tr>
                <tr>
                    <td>/api/files</td>
                    <td>GET</td>
                    <td>获取文件列表</td>
                    <td>path, page, size, viewType</td>
                </tr>
                <tr>
                    <td>/api/files/upload</td>
                    <td>POST</td>
                    <td>上传文件</td>
                    <td>file, path</td>
                </tr>
                <tr>
                    <td>/api/files/download/{id}</td>
                    <td>GET</td>
                    <td>下载文件</td>
                    <td>id</td>
                </tr>
                <tr>
                    <td>/api/files/batch-download</td>
                    <td>POST</td>
                    <td>批量下载</td>
                    <td>fileIds[]</td>
                </tr>
                <tr>
                    <td>/api/files/thumbnail/{id}</td>
                    <td>GET</td>
                    <td>获取缩略图</td>
                    <td>id, size</td>
                </tr>
                <tr>
                    <td>/api/files/preview/{id}</td>
                    <td>GET</td>
                    <td>文件预览</td>
                    <td>id</td>
                </tr>
            </table>

            <h4>3. 搜索API</h4>
            <table>
                <tr>
                    <th>接口</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>参数</th>
                </tr>
                <tr>
                    <td>/api/search/text</td>
                    <td>GET</td>
                    <td>文本搜索</td>
                    <td>query, page, size</td>
                </tr>
                <tr>
                    <td>/api/search/image</td>
                    <td>POST</td>
                    <td>图像搜索</td>
                    <td>imageFile</td>
                </tr>
                <tr>
                    <td>/api/search/advanced</td>
                    <td>POST</td>
                    <td>高级搜索</td>
                    <td>filters, sorting</td>
                </tr>
                <tr>
                    <td>/api/search/suggestions</td>
                    <td>GET</td>
                    <td>搜索建议</td>
                    <td>query</td>
                </tr>
            </table>

            <h4>4. 权限管理API</h4>
            <table>
                <tr>
                    <th>接口</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>参数</th>
                </tr>
                <tr>
                    <td>/api/permissions/check</td>
                    <td>POST</td>
                    <td>检查权限</td>
                    <td>resourceId, action</td>
                </tr>
                <tr>
                    <td>/api/permissions/grant</td>
                    <td>POST</td>
                    <td>授予权限</td>
                    <td>userId, resourceId, permissions</td>
                </tr>
                <tr>
                    <td>/api/permissions/revoke</td>
                    <td>DELETE</td>
                    <td>撤销权限</td>
                    <td>userId, resourceId</td>
                </tr>
            </table>

            <h4>5. 监控统计API</h4>
            <table>
                <tr>
                    <th>接口</th>
                    <th>方法</th>
                    <th>说明</th>
                    <th>参数</th>
                </tr>
                <tr>
                    <td>/api/stats/dashboard</td>
                    <td>GET</td>
                    <td>仪表板数据</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>/api/stats/users</td>
                    <td>GET</td>
                    <td>用户统计</td>
                    <td>startDate, endDate</td>
                </tr>
                <tr>
                    <td>/api/stats/downloads</td>
                    <td>GET</td>
                    <td>下载统计</td>
                    <td>startDate, endDate</td>
                </tr>
                <tr>
                    <td>/api/logs/activities</td>
                    <td>GET</td>
                    <td>活动日志</td>
                    <td>userId, page, size</td>
                </tr>
            </table>
        </div>

        <!-- 界面设计 -->
        <div id="ui" class="section">
            <h2>界面设计与原型</h2>

            <h3>设计原则</h3>
            <ul>
                <li><strong>简洁明了</strong>：界面简洁，操作直观</li>
                <li><strong>响应式设计</strong>：适配不同屏幕尺寸</li>
                <li><strong>一致性</strong>：统一的视觉风格和交互模式</li>
                <li><strong>可访问性</strong>：支持键盘导航和屏幕阅读器</li>
            </ul>

            <h3>主界面原型</h3>

            <h4>1. 登录界面</h4>
            <div class="mockup">
                <div class="window-header">
                    <span>文件共享系统 - 登录</span>
                    <div class="window-controls">
                        <span class="control minimize"></span>
                        <span class="control maximize"></span>
                        <span class="control close"></span>
                    </div>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <h3>企业级文件共享系统</h3>
                    <div style="max-width: 300px; margin: 30px auto;">
                        <input type="text" placeholder="用户名" style="width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                        <input type="password" placeholder="密码" style="width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                        <button class="btn" style="width: 100%; margin: 20px 0;">登录</button>
                        <div style="margin-top: 20px;">
                            <label><input type="checkbox"> 记住密码</label>
                        </div>
                    </div>
                </div>
            </div>

            <h4>2. 主界面布局</h4>
            <div class="mockup">
                <div class="window-header">
                    <span>文件共享系统 - 主界面</span>
                    <div class="window-controls">
                        <span class="control minimize"></span>
                        <span class="control maximize"></span>
                        <span class="control close"></span>
                    </div>
                </div>

                <!-- 顶部工具栏 -->
                <div style="background: #f8f9ff; padding: 10px; border-bottom: 1px solid #ddd;">
                    <input type="text" class="search-bar" placeholder="搜索文件...">
                    <div style="margin-top: 10px;">
                        <button class="btn">文本搜索</button>
                        <button class="btn">图像搜索</button>
                        <button class="btn">上传</button>
                        <button class="btn">下载</button>
                        <span style="float: right;">
                            <select style="padding: 8px;">
                                <option>超大图标</option>
                                <option>大图标</option>
                                <option>中等图标</option>
                                <option>详情</option>
                            </select>
                        </span>
                    </div>
                </div>

                <!-- 侧边栏 -->
                <div class="sidebar">
                    <h4>文件夹</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 5px 0;">📁 设计文件</li>
                        <li style="padding: 5px 0;">📁 产品图片</li>
                        <li style="padding: 5px 0;">📁 营销素材</li>
                        <li style="padding: 5px 0;">📁 文档资料</li>
                    </ul>

                    <h4 style="margin-top: 30px;">快速访问</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 5px 0;">⭐ 收藏夹</li>
                        <li style="padding: 5px 0;">📥 最近下载</li>
                        <li style="padding: 5px 0;">🔍 搜索历史</li>
                    </ul>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <div class="file-grid">
                        <div class="file-item">
                            <div class="file-icon"></div>
                            <div>logo.jpg</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon"></div>
                            <div>banner.psd</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon"></div>
                            <div>product.png</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon"></div>
                            <div>design.ai</div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <span>共 1,234 个文件 | 已选择 0 个 | 总大小: 2.5GB</span>
                    <span style="float: right;">在线用户: 15 | 当前用户: admin</span>
                </div>
            </div>
        </div>

        <!-- 安全设计 -->
        <div id="security" class="section">
            <h2>安全设计方案</h2>

            <h3>安全架构</h3>
            <p>系统采用多层次安全防护体系，从网络层到应用层全方位保护数据安全。</p>

            <h3>身份认证与授权</h3>

            <h4>1. 多因素认证</h4>
            <ul>
                <li><strong>用户名密码</strong>：基础认证方式</li>
                <li><strong>JWT Token</strong>：无状态认证令牌</li>
                <li><strong>会话管理</strong>：安全会话控制</li>
                <li><strong>IP白名单</strong>：限制访问来源</li>
            </ul>

            <h4>2. 权限控制模型</h4>
            <div class="code-block">
// RBAC权限模型
用户(User) -> 角色(Role) -> 权限(Permission) -> 资源(Resource)

权限类型:
- READ: 只读权限
- WRITE: 写入权限
- DELETE: 删除权限
- DOWNLOAD: 下载权限
- UPLOAD: 上传权限
- ADMIN: 管理权限
            </div>

            <h3>数据安全</h3>

            <h4>1. 传输安全</h4>
            <ul>
                <li><strong>HTTPS加密</strong>：所有通信使用TLS 1.3加密</li>
                <li><strong>证书验证</strong>：严格的SSL证书验证</li>
                <li><strong>数据完整性</strong>：传输数据完整性校验</li>
            </ul>

            <h4>2. 存储安全</h4>
            <ul>
                <li><strong>密码加密</strong>：使用bcrypt加密存储密码</li>
                <li><strong>敏感数据加密</strong>：AES-256加密敏感信息</li>
                <li><strong>文件隔离</strong>：原文件与系统数据完全隔离</li>
                <li><strong>备份加密</strong>：备份数据加密存储</li>
            </ul>

            <h3>访问控制</h3>

            <h4>1. 网络访问控制</h4>
            <ul>
                <li><strong>防火墙规则</strong>：严格的端口和IP访问控制</li>
                <li><strong>VPN支持</strong>：支持VPN安全接入</li>
                <li><strong>内外网隔离</strong>：内网和外网访问分离</li>
                <li><strong>DDoS防护</strong>：分布式拒绝服务攻击防护</li>
            </ul>

            <h4>2. 应用层访问控制</h4>
            <ul>
                <li><strong>限流控制</strong>：API访问频率限制</li>
                <li><strong>会话超时</strong>：自动会话超时机制</li>
                <li><strong>并发控制</strong>：用户并发登录限制</li>
                <li><strong>异常检测</strong>：异常访问行为检测</li>
            </ul>

            <h3>监控与审计</h3>

            <h4>1. 安全监控</h4>
            <ul>
                <li><strong>实时监控</strong>：用户行为实时监控</li>
                <li><strong>异常告警</strong>：安全事件自动告警</li>
                <li><strong>威胁检测</strong>：恶意行为检测</li>
                <li><strong>入侵检测</strong>：系统入侵检测</li>
            </ul>

            <h4>2. 审计日志</h4>
            <ul>
                <li><strong>操作日志</strong>：完整记录用户操作</li>
                <li><strong>访问日志</strong>：记录所有访问请求</li>
                <li><strong>安全日志</strong>：记录安全相关事件</li>
                <li><strong>系统日志</strong>：记录系统运行状态</li>
            </ul>

            <h3>数据保护</h3>

            <h4>1. 数据备份</h4>
            <ul>
                <li><strong>自动备份</strong>：定时自动备份重要数据</li>
                <li><strong>增量备份</strong>：高效的增量备份策略</li>
                <li><strong>异地备份</strong>：多地备份保障</li>
                <li><strong>备份验证</strong>：定期备份完整性验证</li>
            </ul>

            <h4>2. 灾难恢复</h4>
            <ul>
                <li><strong>快速恢复</strong>：快速数据恢复机制</li>
                <li><strong>故障转移</strong>：自动故障转移</li>
                <li><strong>数据同步</strong>：实时数据同步</li>
                <li><strong>恢复测试</strong>：定期恢复演练</li>
            </ul>
        </div>

        <!-- 部署方案 -->
        <div id="deployment" class="section">
            <h2>部署方案</h2>

            <h3>部署架构</h3>
            <p>系统支持单机部署和集群部署两种模式，可根据实际需求选择合适的部署方案。</p>

            <h3>环境要求</h3>

            <h4>硬件要求</h4>
            <table>
                <tr>
                    <th>组件</th>
                    <th>最低配置</th>
                    <th>推荐配置</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>4核心</td>
                    <td>8核心以上</td>
                    <td>支持多线程处理</td>
                </tr>
                <tr>
                    <td>内存</td>
                    <td>8GB</td>
                    <td>16GB以上</td>
                    <td>缓存和并发处理</td>
                </tr>
                <tr>
                    <td>存储</td>
                    <td>100GB SSD</td>
                    <td>500GB SSD</td>
                    <td>系统和缓存存储</td>
                </tr>
                <tr>
                    <td>网络</td>
                    <td>100Mbps</td>
                    <td>1Gbps</td>
                    <td>文件传输带宽</td>
                </tr>
            </table>

            <h4>软件要求</h4>
            <ul>
                <li><strong>操作系统</strong>：Windows Server 2019+, Ubuntu 20.04+, CentOS 8+</li>
                <li><strong>运行时</strong>：.NET 8.0 Runtime</li>
                <li><strong>数据库</strong>：PostgreSQL 14+</li>
                <li><strong>缓存</strong>：Redis 6.0+</li>
                <li><strong>Web服务器</strong>：Nginx 1.20+ (可选)</li>
            </ul>

            <h3>部署步骤</h3>

            <h4>1. 单机部署</h4>
            <div class="timeline">
                <div class="timeline-item">
                    <h4>环境准备</h4>
                    <p>安装.NET 8.0运行时、PostgreSQL数据库、Redis缓存服务</p>
                </div>
                <div class="timeline-item">
                    <h4>数据库初始化</h4>
                    <p>创建数据库、执行初始化脚本、配置数据库连接</p>
                </div>
                <div class="timeline-item">
                    <h4>应用部署</h4>
                    <p>部署后端API服务、配置文件设置、启动服务</p>
                </div>
                <div class="timeline-item">
                    <h4>前端部署</h4>
                    <p>构建前端应用、配置Web服务器、部署静态资源</p>
                </div>
                <div class="timeline-item">
                    <h4>系统配置</h4>
                    <p>配置权限、创建管理员账户、测试系统功能</p>
                </div>
            </div>

            <h4>2. 集群部署</h4>
            <div class="architecture-diagram">
                <h4>集群架构图</h4>
                <div class="layer">
                    <strong>负载均衡层</strong><br>
                    Nginx Load Balancer
                </div>
                <div class="layer">
                    <strong>应用服务层</strong><br>
                    API Server 1 | API Server 2 | API Server 3
                </div>
                <div class="layer">
                    <strong>数据库层</strong><br>
                    PostgreSQL Master | PostgreSQL Slave | Redis Cluster
                </div>
                <div class="layer">
                    <strong>存储层</strong><br>
                    NFS/GlusterFS 分布式文件系统
                </div>
            </div>

            <h3>配置文件</h3>

            <h4>应用配置 (appsettings.json)</h4>
            <div class="code-block">
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=FileShareDB;Username=postgres;Password=password",
    "RedisConnection": "localhost:6379"
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key",
    "Issuer": "FileShareSystem",
    "Audience": "FileShareUsers",
    "ExpirationMinutes": 60
  },
  "FileSettings": {
    "StoragePath": "/data/files",
    "ThumbnailPath": "/data/thumbnails",
    "MaxFileSize": 104857600,
    "AllowedExtensions": [".jpg", ".png", ".psd", ".ai", ".eps", ".tif"]
  },
  "SecuritySettings": {
    "EnableRateLimit": true,
    "MaxRequestsPerMinute": 100,
    "EnableIpWhitelist": false,
    "AllowedIps": []
  }
}
            </div>

            <h3>运维管理</h3>

            <h4>1. 监控指标</h4>
            <ul>
                <li><strong>系统指标</strong>：CPU、内存、磁盘、网络使用率</li>
                <li><strong>应用指标</strong>：请求量、响应时间、错误率</li>
                <li><strong>业务指标</strong>：用户活跃度、文件访问量、存储使用量</li>
            </ul>

            <h4>2. 日志管理</h4>
            <ul>
                <li><strong>日志收集</strong>：统一日志收集和存储</li>
                <li><strong>日志分析</strong>：日志分析和告警</li>
                <li><strong>日志轮转</strong>：自动日志轮转和清理</li>
            </ul>

            <h4>3. 备份策略</h4>
            <ul>
                <li><strong>数据库备份</strong>：每日全量备份，每小时增量备份</li>
                <li><strong>文件备份</strong>：定期文件系统备份</li>
                <li><strong>配置备份</strong>：系统配置文件备份</li>
            </ul>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(link => {
                link.style.background = '';
                link.style.color = '#667eea';
            });

            event.target.style.background = '#667eea';
            event.target.style.color = 'white';
        }

        // 默认显示第一个section
        document.addEventListener('DOMContentLoaded', function() {
            showSection('overview');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python版企业级文件共享系统 - 开发文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .nav {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .nav li {
            margin: 0 15px;
        }
        
        .nav a {
            text-decoration: none;
            color: #3776ab;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .nav a:hover {
            background: #3776ab;
            color: white;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #3776ab;
            border-bottom: 2px solid #3776ab;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
        }
        
        h3 {
            color: #2c5282;
            margin: 20px 0 15px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3776ab;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .architecture-diagram {
            background: #f8f9ff;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .layer {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #3776ab;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #3776ab;
            color: white;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .python-code {
            background: #1e3a8a;
            color: #fbbf24;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #3776ab;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3776ab;
        }
        
        .comparison-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .pros {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .cons {
            background: #f8d7da;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 Python版企业级文件共享系统</h1>
            <p>基于Python生态的高性能文件管理解决方案</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#" onclick="showSection('overview')">技术概述</a></li>
                <li><a href="#" onclick="showSection('architecture')">系统架构</a></li>
                <li><a href="#" onclick="showSection('backend')">后端设计</a></li>
                <li><a href="#" onclick="showSection('database')">数据库设计</a></li>
                <li><a href="#" onclick="showSection('search')">搜索引擎</a></li>
                <li><a href="#" onclick="showSection('security')">安全方案</a></li>
                <li><a href="#" onclick="showSection('deployment')">部署方案</a></li>
                <li><a href="#" onclick="showSection('code')">代码示例</a></li>
            </ul>
        </nav>

        <!-- 技术概述 -->
        <div id="overview" class="section active">
            <h2>Python技术栈概述</h2>
            
            <h3>为什么选择Python？</h3>
            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ Python的优势</h4>
                    <ul>
                        <li><strong>开发效率高</strong>：语法简洁，开发速度快</li>
                        <li><strong>生态丰富</strong>：丰富的第三方库支持</li>
                        <li><strong>AI/ML支持</strong>：图像识别、机器学习库完善</li>
                        <li><strong>跨平台</strong>：Windows、Linux、macOS全支持</li>
                        <li><strong>社区活跃</strong>：问题解决和技术支持丰富</li>
                        <li><strong>维护成本低</strong>：代码可读性强，易于维护</li>
                    </ul>
                </div>
                <div class="cons">
                    <h4>⚠️ 需要注意的点</h4>
                    <ul>
                        <li><strong>性能考虑</strong>：需要合理的架构设计和优化</li>
                        <li><strong>并发处理</strong>：使用异步编程提升性能</li>
                        <li><strong>内存管理</strong>：大文件处理需要优化内存使用</li>
                        <li><strong>部署复杂度</strong>：需要合理的容器化部署</li>
                    </ul>
                </div>
            </div>
            
            <h3>核心技术栈</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>🚀 Web框架</h4>
                    <p>FastAPI<br>高性能异步API框架</p>
                </div>
                <div class="tech-item">
                    <h4>🗄️ 数据库</h4>
                    <p>PostgreSQL + Redis<br>关系型数据库 + 缓存</p>
                </div>
                <div class="tech-item">
                    <h4>🔍 搜索引擎</h4>
                    <p>Elasticsearch + OpenCV<br>全文搜索 + 图像识别</p>
                </div>
                <div class="tech-item">
                    <h4>🤖 AI/ML</h4>
                    <p>TensorFlow + OpenCV<br>图像识别和处理</p>
                </div>
                <div class="tech-item">
                    <h4>⚡ 异步处理</h4>
                    <p>Celery + Redis<br>任务队列和异步处理</p>
                </div>
                <div class="tech-item">
                    <h4>🌐 前端</h4>
                    <p>React + TypeScript<br>现代化Web界面</p>
                </div>
            </div>
            
            <h3>完整技术栈清单</h3>
            <table>
                <tr>
                    <th>分类</th>
                    <th>技术选型</th>
                    <th>版本要求</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>Python版本</td>
                    <td>Python</td>
                    <td>3.11+</td>
                    <td>最新稳定版本，性能优化</td>
                </tr>
                <tr>
                    <td>Web框架</td>
                    <td>FastAPI</td>
                    <td>0.104+</td>
                    <td>高性能异步API框架</td>
                </tr>
                <tr>
                    <td>ORM</td>
                    <td>SQLAlchemy</td>
                    <td>2.0+</td>
                    <td>现代化ORM，支持异步</td>
                </tr>
                <tr>
                    <td>数据库</td>
                    <td>PostgreSQL</td>
                    <td>15+</td>
                    <td>企业级关系型数据库</td>
                </tr>
                <tr>
                    <td>缓存</td>
                    <td>Redis</td>
                    <td>7.0+</td>
                    <td>高性能缓存数据库</td>
                </tr>
                <tr>
                    <td>搜索引擎</td>
                    <td>Elasticsearch</td>
                    <td>8.0+</td>
                    <td>分布式搜索引擎</td>
                </tr>
                <tr>
                    <td>任务队列</td>
                    <td>Celery</td>
                    <td>5.3+</td>
                    <td>分布式任务队列</td>
                </tr>
                <tr>
                    <td>图像处理</td>
                    <td>OpenCV</td>
                    <td>4.8+</td>
                    <td>计算机视觉库</td>
                </tr>
                <tr>
                    <td>机器学习</td>
                    <td>TensorFlow</td>
                    <td>2.13+</td>
                    <td>深度学习框架</td>
                </tr>
                <tr>
                    <td>Web服务器</td>
                    <td>Uvicorn</td>
                    <td>0.24+</td>
                    <td>ASGI服务器</td>
                </tr>
            </table>
        </div>

        <!-- 系统架构 -->
        <div id="architecture" class="section">
            <h2>Python系统架构设计</h2>

            <h3>整体架构图</h3>
            <div class="architecture-diagram">
                <h4>微服务架构设计</h4>
                <div class="layer">
                    <strong>前端层 (Frontend)</strong><br>
                    React Web App | Electron Desktop App | Mobile App
                </div>
                <div class="layer">
                    <strong>API网关层 (API Gateway)</strong><br>
                    Nginx + FastAPI | 负载均衡 | 认证授权 | 限流控制
                </div>
                <div class="layer">
                    <strong>业务服务层 (Business Services)</strong><br>
                    用户服务 | 文件服务 | 搜索服务 | 权限服务 | 监控服务
                </div>
                <div class="layer">
                    <strong>数据处理层 (Data Processing)</strong><br>
                    Celery任务队列 | 图像处理 | 文件索引 | 缩略图生成
                </div>
                <div class="layer">
                    <strong>数据存储层 (Data Storage)</strong><br>
                    PostgreSQL | Redis | Elasticsearch | 文件系统
                </div>
            </div>

            <h3>核心服务模块</h3>

            <h4>1. API服务 (FastAPI)</h4>
            <div class="python-code">
# main.py - FastAPI应用入口
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
import uvicorn

app = FastAPI(
    title="企业文件共享系统",
    description="基于Python的高性能文件管理API",
    version="1.0.0"
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由注册
from routers import auth, files, search, admin
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(files.router, prefix="/api/files", tags=["文件"])
app.include_router(search.router, prefix="/api/search", tags=["搜索"])
app.include_router(admin.router, prefix="/api/admin", tags=["管理"])
            </div>

            <h4>2. 数据库模型 (SQLAlchemy)</h4>
            <div class="python-code">
# models.py - 数据库模型定义
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    password_hash = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    files = relationship("FileEntry", back_populates="owner")
    activities = relationship("UserActivity", back_populates="user")

class FileEntry(Base):
    __tablename__ = "file_entries"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), index=True)
    file_path = Column(String(500))
    file_size = Column(Integer)
    file_type = Column(String(50))
    file_hash = Column(String(64), index=True)
    owner_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    owner = relationship("User", back_populates="files")
    thumbnails = relationship("FileThumbnail", back_populates="file")
            </div>

            <h4>3. 异步任务处理 (Celery)</h4>
            <div class="python-code">
# tasks.py - Celery异步任务
from celery import Celery
import cv2
import numpy as np
from PIL import Image
import tensorflow as tf

# Celery配置
celery_app = Celery(
    "file_system",
    broker="redis://localhost:6379/0",
    backend="redis://localhost:6379/0"
)

@celery_app.task
def generate_thumbnail(file_path: str, sizes: list = [150, 300, 600]):
    """生成文件缩略图"""
    try:
        with Image.open(file_path) as img:
            thumbnails = {}
            for size in sizes:
                # 保持宽高比缩放
                img.thumbnail((size, size), Image.Resampling.LANCZOS)
                thumbnail_path = f"{file_path}_thumb_{size}.jpg"
                img.save(thumbnail_path, "JPEG", quality=85)
                thumbnails[size] = thumbnail_path
            return thumbnails
    except Exception as e:
        return {"error": str(e)}

@celery_app.task
def extract_image_features(file_path: str):
    """提取图像特征用于相似度搜索"""
    try:
        # 使用预训练模型提取特征
        model = tf.keras.applications.ResNet50(
            weights='imagenet',
            include_top=False,
            pooling='avg'
        )

        img = cv2.imread(file_path)
        img = cv2.resize(img, (224, 224))
        img = np.expand_dims(img, axis=0)
        img = tf.keras.applications.resnet50.preprocess_input(img)

        features = model.predict(img)
        return features.tolist()
    except Exception as e:
        return {"error": str(e)}
            </div>
        </div>

        <!-- 后端设计 -->
        <div id="backend" class="section">
            <h2>后端详细设计</h2>

            <h3>项目结构</h3>
            <div class="python-code">
file_share_system/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py              # 配置文件
│   ├── database.py            # 数据库连接
│   ├── models/                # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── file.py
│   │   └── permission.py
│   ├── routers/               # API路由
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── files.py
│   │   ├── search.py
│   │   └── admin.py
│   ├── services/              # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── file_service.py
│   │   ├── search_service.py
│   │   └── permission_service.py
│   ├── utils/                 # 工具函数
│   │   ├── __init__.py
│   │   ├── security.py
│   │   ├── file_utils.py
│   │   └── image_utils.py
│   └── tasks/                 # Celery任务
│       ├── __init__.py
│       ├── file_tasks.py
│       └── search_tasks.py
├── tests/                     # 测试文件
├── docker/                    # Docker配置
├── requirements.txt           # Python依赖
├── docker-compose.yml         # 容器编排
└── README.md
            </div>

            <h3>核心依赖包 (requirements.txt)</h3>
            <div class="python-code">
# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1
psycopg2-binary==2.9.9

# 缓存和任务队列
redis==5.0.1
celery==5.3.4
flower==2.0.1

# 搜索引擎
elasticsearch==8.11.0
elasticsearch-dsl==8.11.0

# 图像处理和AI
opencv-python==********
Pillow==10.1.0
tensorflow==2.13.0
numpy==1.24.3
scikit-learn==1.3.2

# 安全认证
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 文件处理
aiofiles==23.2.1
python-magic==0.4.27

# 配置和环境
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 监控和日志
structlog==23.2.0
prometheus-client==0.19.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
            </div>

            <h3>配置管理</h3>
            <div class="python-code">
# config.py - 应用配置
from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "企业文件共享系统"
    app_version: str = "1.0.0"
    debug: bool = False

    # 数据库配置
    database_url: str = "postgresql+asyncpg://user:pass@localhost/filedb"
    redis_url: str = "redis://localhost:6379/0"
    elasticsearch_url: str = "http://localhost:9200"

    # 安全配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # 文件配置
    upload_path: str = "/data/uploads"
    thumbnail_path: str = "/data/thumbnails"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: List[str] = [
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff",
        ".psd", ".ai", ".eps", ".pdf", ".doc", ".docx"
    ]

    # 搜索配置
    elasticsearch_index: str = "files"
    search_result_limit: int = 100

    # Celery配置
    celery_broker_url: str = "redis://localhost:6379/0"
    celery_result_backend: str = "redis://localhost:6379/0"

    # 安全配置
    cors_origins: List[str] = ["http://localhost:3000"]
    rate_limit_per_minute: int = 100

    class Config:
        env_file = ".env"

settings = Settings()
            </div>
        </div>

        <!-- 数据库设计 -->
        <div id="database" class="section">
            <h2>数据库设计</h2>

            <h3>数据库迁移 (Alembic)</h3>
            <div class="python-code">
# alembic/versions/001_initial_migration.py
"""Initial migration

Revision ID: 001
Revises:
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # 用户表
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(50), nullable=False),
        sa.Column('email', sa.String(100), nullable=False),
        sa.Column('password_hash', sa.String(255), nullable=False),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('is_admin', sa.Boolean(), default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username'),
        sa.UniqueConstraint('email')
    )
    op.create_index('ix_users_username', 'users', ['username'])
    op.create_index('ix_users_email', 'users', ['email'])

    # 文件表
    op.create_table('file_entries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('filename', sa.String(255), nullable=False),
        sa.Column('file_path', sa.String(500), nullable=False),
        sa.Column('file_size', sa.BigInteger(), nullable=False),
        sa.Column('file_type', sa.String(50), nullable=False),
        sa.Column('file_hash', sa.String(64), nullable=False),
        sa.Column('mime_type', sa.String(100), nullable=True),
        sa.Column('owner_id', sa.Integer(), nullable=False),
        sa.Column('directory_id', sa.Integer(), nullable=True),
        sa.Column('is_public', sa.Boolean(), default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_file_entries_filename', 'file_entries', ['filename'])
    op.create_index('ix_file_entries_file_hash', 'file_entries', ['file_hash'])
    op.create_index('ix_file_entries_file_type', 'file_entries', ['file_type'])

def downgrade():
    op.drop_table('file_entries')
    op.drop_table('users')
            </div>

            <h3>数据库连接管理</h3>
            <div class="python-code">
# database.py - 数据库连接
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# 创建异步引擎
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

Base = declarative_base()

# 依赖注入：获取数据库会话
async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# Redis连接
import redis.asyncio as redis

redis_client = redis.from_url(
    settings.redis_url,
    encoding="utf-8",
    decode_responses=True,
    max_connections=20
)

# Elasticsearch连接
from elasticsearch import AsyncElasticsearch

es_client = AsyncElasticsearch(
    [settings.elasticsearch_url],
    max_retries=3,
    retry_on_timeout=True
)
            </div>
        </div>

        <!-- 搜索引擎 -->
        <div id="search" class="section">
            <h2>双搜索引擎设计</h2>

            <h3>1. 文本搜索引擎 (Elasticsearch)</h3>

            <h4>索引配置</h4>
            <div class="python-code">
# search/elasticsearch_config.py
from elasticsearch import AsyncElasticsearch

# 文件索引映射配置
FILE_INDEX_MAPPING = {
    "mappings": {
        "properties": {
            "filename": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "fields": {
                    "keyword": {"type": "keyword"}
                }
            },
            "content": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            },
            "file_type": {"type": "keyword"},
            "file_size": {"type": "long"},
            "file_path": {"type": "keyword"},
            "owner_id": {"type": "integer"},
            "tags": {
                "type": "text",
                "analyzer": "ik_max_word"
            },
            "created_at": {"type": "date"},
            "updated_at": {"type": "date"}
        }
    },
    "settings": {
        "number_of_shards": 3,
        "number_of_replicas": 1,
        "analysis": {
            "analyzer": {
                "filename_analyzer": {
                    "type": "custom",
                    "tokenizer": "standard",
                    "filter": ["lowercase", "stop"]
                }
            }
        }
    }
}

async def create_file_index(es_client: AsyncElasticsearch):
    """创建文件索引"""
    if not await es_client.indices.exists(index="files"):
        await es_client.indices.create(
            index="files",
            body=FILE_INDEX_MAPPING
        )
            </div>

            <h4>搜索服务实现</h4>
            <div class="python-code">
# services/search_service.py
from elasticsearch import AsyncElasticsearch
from typing import List, Dict, Optional
import asyncio

class SearchService:
    def __init__(self, es_client: AsyncElasticsearch):
        self.es = es_client

    async def text_search(
        self,
        query: str,
        file_types: Optional[List[str]] = None,
        size_range: Optional[Dict] = None,
        date_range: Optional[Dict] = None,
        user_permissions: List[str] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict:
        """高级文本搜索"""

        # 构建搜索查询
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": [
                                    "filename^3",      # 文件名权重最高
                                    "content^2",       # 内容权重中等
                                    "tags^1.5"         # 标签权重较低
                                ],
                                "type": "best_fields",
                                "fuzziness": "AUTO"
                            }
                        }
                    ],
                    "filter": []
                }
            },
            "highlight": {
                "fields": {
                    "filename": {
                        "pre_tags": ["<mark>"],
                        "post_tags": ["</mark>"]
                    },
                    "content": {
                        "pre_tags": ["<mark>"],
                        "post_tags": ["</mark>"],
                        "fragment_size": 150,
                        "number_of_fragments": 3
                    }
                }
            },
            "sort": [
                {"_score": {"order": "desc"}},
                {"created_at": {"order": "desc"}}
            ],
            "from": (page - 1) * size,
            "size": size
        }

        # 添加文件类型过滤
        if file_types:
            search_body["query"]["bool"]["filter"].append({
                "terms": {"file_type": file_types}
            })

        # 添加文件大小过滤
        if size_range:
            search_body["query"]["bool"]["filter"].append({
                "range": {"file_size": size_range}
            })

        # 添加日期过滤
        if date_range:
            search_body["query"]["bool"]["filter"].append({
                "range": {"created_at": date_range}
            })

        # 添加权限过滤
        if user_permissions:
            search_body["query"]["bool"]["filter"].append({
                "terms": {"file_path": user_permissions}
            })

        # 执行搜索
        result = await self.es.search(
            index="files",
            body=search_body
        )

        return {
            "total": result["hits"]["total"]["value"],
            "results": [
                {
                    **hit["_source"],
                    "score": hit["_score"],
                    "highlights": hit.get("highlight", {})
                }
                for hit in result["hits"]["hits"]
            ],
            "aggregations": result.get("aggregations", {}),
            "took": result["took"]
        }

    async def suggest_search(self, query: str, size: int = 5) -> List[str]:
        """搜索建议"""
        suggest_body = {
            "suggest": {
                "filename_suggest": {
                    "prefix": query,
                    "completion": {
                        "field": "filename.suggest",
                        "size": size
                    }
                }
            }
        }

        result = await self.es.search(
            index="files",
            body=suggest_body
        )

        suggestions = []
        for option in result["suggest"]["filename_suggest"][0]["options"]:
            suggestions.append(option["text"])

        return suggestions
            </div>

            <h3>2. 图像搜索引擎 (AI驱动)</h3>

            <h4>图像特征提取</h4>
            <div class="python-code">
# services/image_search_service.py
import cv2
import numpy as np
import tensorflow as tf
from sklearn.metrics.pairwise import cosine_similarity
from typing import List, Tuple, Dict
import pickle

class ImageSearchService:
    def __init__(self):
        # 加载预训练模型
        self.feature_extractor = tf.keras.applications.ResNet50(
            weights='imagenet',
            include_top=False,
            pooling='avg'
        )

        # 图像预处理
        self.preprocess = tf.keras.applications.resnet50.preprocess_input

    def extract_features(self, image_path: str) -> np.ndarray:
        """提取图像特征向量"""
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 转换颜色空间
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # 调整大小
            img = cv2.resize(img, (224, 224))

            # 预处理
            img = np.expand_dims(img, axis=0)
            img = self.preprocess(img)

            # 提取特征
            features = self.feature_extractor.predict(img, verbose=0)

            # 归一化
            features = features / np.linalg.norm(features)

            return features.flatten()

        except Exception as e:
            raise Exception(f"特征提取失败: {str(e)}")

    def extract_color_histogram(self, image_path: str) -> np.ndarray:
        """提取颜色直方图特征"""
        img = cv2.imread(image_path)

        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 计算直方图
        hist = cv2.calcHist([hsv], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])

        # 归一化
        hist = cv2.normalize(hist, hist).flatten()

        return hist

    def find_similar_images(
        self,
        query_features: np.ndarray,
        database_features: List[Tuple[int, np.ndarray]],
        threshold: float = 0.8,
        top_k: int = 20
    ) -> List[Tuple[int, float]]:
        """查找相似图像"""
        similarities = []

        for file_id, features in database_features:
            # 计算余弦相似度
            similarity = cosine_similarity(
                query_features.reshape(1, -1),
                features.reshape(1, -1)
            )[0][0]

            if similarity >= threshold:
                similarities.append((file_id, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        return similarities[:top_k]

    async def search_by_image(
        self,
        query_image_path: str,
        threshold: float = 0.8,
        top_k: int = 20
    ) -> List[Dict]:
        """基于图像的相似度搜索"""

        # 提取查询图像特征
        query_features = self.extract_features(query_image_path)

        # 从数据库获取所有图像特征
        # 这里应该从数据库或缓存中获取预计算的特征
        database_features = await self.get_all_image_features()

        # 查找相似图像
        similar_images = self.find_similar_images(
            query_features,
            database_features,
            threshold,
            top_k
        )

        # 获取文件详细信息
        results = []
        for file_id, similarity in similar_images:
            file_info = await self.get_file_info(file_id)
            results.append({
                **file_info,
                "similarity": similarity
            })

        return results
            </div>
        </div>

        <!-- 安全方案 -->
        <div id="security" class="section">
            <h2>Python安全方案</h2>

            <h3>认证与授权</h3>

            <h4>JWT认证实现</h4>
            <div class="python-code">
# utils/security.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

security = HTTPBearer()

class SecurityManager:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """生成密码哈希"""
        return pwd_context.hash(password)

    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的认证凭据",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return payload
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户"""
    token = credentials.credentials
    payload = SecurityManager.verify_token(token)
    username = payload.get("sub")

    user = await get_user_by_username(db, username)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )

    return user
            </div>

            <h4>权限控制系统</h4>
            <div class="python-code">
# services/permission_service.py
from enum import Enum
from typing import List, Dict, Optional
from sqlalchemy.orm import Session

class PermissionType(Enum):
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    DOWNLOAD = "download"
    UPLOAD = "upload"
    ADMIN = "admin"

class PermissionService:
    @staticmethod
    async def check_file_permission(
        user_id: int,
        file_id: int,
        permission: PermissionType,
        db: Session
    ) -> bool:
        """检查文件权限"""

        # 1. 检查用户是否是文件所有者
        file_entry = await db.get(FileEntry, file_id)
        if file_entry and file_entry.owner_id == user_id:
            return True

        # 2. 检查用户组权限
        user_groups = await get_user_groups(db, user_id)
        for group in user_groups:
            group_permissions = await get_group_permissions(db, group.id, file_id)
            if permission.value in group_permissions:
                return True

        # 3. 检查直接用户权限
        user_permissions = await get_user_permissions(db, user_id, file_id)
        if permission.value in user_permissions:
            return True

        # 4. 检查公共文件权限
        if file_entry and file_entry.is_public and permission == PermissionType.READ:
            return True

        return False

    @staticmethod
    async def check_directory_permission(
        user_id: int,
        directory_path: str,
        permission: PermissionType,
        db: Session
    ) -> bool:
        """检查目录权限"""

        # 获取用户可访问的目录列表
        accessible_paths = await get_user_accessible_paths(db, user_id)

        # 检查路径是否在可访问列表中
        for path in accessible_paths:
            if directory_path.startswith(path):
                return True

        return False

    @staticmethod
    async def get_user_accessible_files(
        user_id: int,
        db: Session,
        file_type: Optional[str] = None
    ) -> List[int]:
        """获取用户可访问的文件ID列表"""

        accessible_files = []

        # 用户拥有的文件
        owned_files = await get_user_owned_files(db, user_id, file_type)
        accessible_files.extend([f.id for f in owned_files])

        # 通过权限可访问的文件
        permitted_files = await get_user_permitted_files(db, user_id, file_type)
        accessible_files.extend([f.id for f in permitted_files])

        # 公共文件
        public_files = await get_public_files(db, file_type)
        accessible_files.extend([f.id for f in public_files])

        return list(set(accessible_files))  # 去重
            </div>

            <h3>数据安全</h3>

            <h4>文件加密存储</h4>
            <div class="python-code">
# utils/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class FileEncryption:
    def __init__(self, password: bytes):
        """初始化加密器"""
        salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        self.cipher = Fernet(key)
        self.salt = salt

    def encrypt_file(self, file_path: str, encrypted_path: str):
        """加密文件"""
        with open(file_path, 'rb') as file:
            file_data = file.read()

        encrypted_data = self.cipher.encrypt(file_data)

        with open(encrypted_path, 'wb') as encrypted_file:
            encrypted_file.write(self.salt + encrypted_data)

    def decrypt_file(self, encrypted_path: str, decrypted_path: str):
        """解密文件"""
        with open(encrypted_path, 'rb') as encrypted_file:
            salt = encrypted_file.read(16)
            encrypted_data = encrypted_file.read()

        decrypted_data = self.cipher.decrypt(encrypted_data)

        with open(decrypted_path, 'wb') as decrypted_file:
            decrypted_file.write(decrypted_data)

# 敏感数据加密
class DataEncryption:
    def __init__(self, key: str):
        self.cipher = Fernet(key.encode())

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
            </div>

            <h3>安全监控</h3>

            <h4>行为监控系统</h4>
            <div class="python-code">
# services/monitoring_service.py
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import asyncio
from collections import defaultdict

class SecurityMonitor:
    def __init__(self):
        self.failed_attempts = defaultdict(list)
        self.suspicious_activities = []
        self.rate_limits = defaultdict(list)

    async def log_login_attempt(
        self,
        username: str,
        ip_address: str,
        success: bool,
        user_agent: str
    ):
        """记录登录尝试"""
        attempt = {
            "username": username,
            "ip_address": ip_address,
            "success": success,
            "timestamp": datetime.utcnow(),
            "user_agent": user_agent
        }

        if not success:
            self.failed_attempts[ip_address].append(attempt)

            # 检查是否需要封禁IP
            await self.check_brute_force_attack(ip_address)

        # 记录到数据库
        await self.save_login_log(attempt)

    async def check_brute_force_attack(self, ip_address: str):
        """检查暴力破解攻击"""
        now = datetime.utcnow()
        recent_attempts = [
            attempt for attempt in self.failed_attempts[ip_address]
            if now - attempt["timestamp"] < timedelta(minutes=15)
        ]

        if len(recent_attempts) >= 5:
            # 触发安全警报
            await self.trigger_security_alert(
                "BRUTE_FORCE_ATTACK",
                f"IP {ip_address} 在15分钟内失败登录{len(recent_attempts)}次",
                {"ip_address": ip_address, "attempts": len(recent_attempts)}
            )

            # 封禁IP
            await self.ban_ip_address(ip_address, duration=timedelta(hours=1))

    async def check_rate_limit(
        self,
        user_id: int,
        action: str,
        limit: int,
        window: timedelta
    ) -> bool:
        """检查速率限制"""
        now = datetime.utcnow()
        key = f"{user_id}:{action}"

        # 清理过期记录
        self.rate_limits[key] = [
            timestamp for timestamp in self.rate_limits[key]
            if now - timestamp < window
        ]

        # 检查是否超过限制
        if len(self.rate_limits[key]) >= limit:
            return False

        # 记录当前请求
        self.rate_limits[key].append(now)
        return True

    async def detect_suspicious_activity(
        self,
        user_id: int,
        action: str,
        details: Dict
    ):
        """检测可疑活动"""

        # 检查异常下载行为
        if action == "download":
            download_count = await self.get_user_download_count(
                user_id,
                timedelta(hours=1)
            )
            if download_count > 100:  # 1小时内下载超过100个文件
                await self.trigger_security_alert(
                    "EXCESSIVE_DOWNLOAD",
                    f"用户 {user_id} 在1小时内下载了{download_count}个文件",
                    {"user_id": user_id, "count": download_count}
                )

        # 检查异常搜索行为
        if action == "search":
            search_count = await self.get_user_search_count(
                user_id,
                timedelta(minutes=10)
            )
            if search_count > 50:  # 10分钟内搜索超过50次
                await self.trigger_security_alert(
                    "EXCESSIVE_SEARCH",
                    f"用户 {user_id} 在10分钟内搜索了{search_count}次",
                    {"user_id": user_id, "count": search_count}
                )

    async def trigger_security_alert(
        self,
        alert_type: str,
        message: str,
        details: Dict
    ):
        """触发安全警报"""
        alert = {
            "type": alert_type,
            "message": message,
            "details": details,
            "timestamp": datetime.utcnow(),
            "status": "ACTIVE"
        }

        # 保存到数据库
        await self.save_security_alert(alert)

        # 发送通知给管理员
        await self.notify_administrators(alert)

        # 记录日志
        logger.warning(f"安全警报: {alert_type} - {message}")
            </div>
        </div>

        <!-- 部署方案 -->
        <div id="deployment" class="section">
            <h2>Python部署方案</h2>

            <h3>Docker容器化部署</h3>

            <h4>Dockerfile</h4>
            <div class="python-code">
# Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    libmagic1 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
            </div>

            <h4>Docker Compose配置</h4>
            <div class="python-code">
# docker-compose.yml
version: '3.8'

services:
  # Web应用
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/filedb
      - REDIS_URL=redis://redis:6379/0
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
      - elasticsearch
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: filedb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build: .
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/filedb
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./uploads:/app/uploads
      - ./thumbnails:/app/thumbnails
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Beat (定时任务)
  celery-beat:
    build: .
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/filedb
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Flower (Celery监控)
  flower:
    build: .
    command: celery -A app.tasks.celery_app flower
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
            </div>

            <h3>生产环境配置</h3>

            <h4>Nginx配置</h4>
            <div class="python-code">
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app {
        server web:8000;
    }

    # 限制请求大小
    client_max_body_size 100M;

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name your-domain.com;

        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # 静态文件
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API代理
        location /api/ {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 文件上传超时
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
        }

        # WebSocket支持
        location /ws/ {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        # 前端应用
        location / {
            try_files $uri $uri/ /index.html;
            root /var/www/static;
        }
    }
}
            </div>

            <h4>环境变量配置</h4>
            <div class="python-code">
# .env.production
# 应用配置
APP_NAME=企业文件共享系统
APP_VERSION=1.0.0
DEBUG=False
ENVIRONMENT=production

# 数据库配置
DATABASE_URL=postgresql+asyncpg://user:password@db-host:5432/filedb
REDIS_URL=redis://redis-host:6379/0
ELASTICSEARCH_URL=http://es-host:9200

# 安全配置
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件配置
UPLOAD_PATH=/data/uploads
THUMBNAIL_PATH=/data/thumbnails
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.gif,.bmp,.tiff,.psd,.ai,.eps,.pdf

# 搜索配置
ELASTICSEARCH_INDEX=files
SEARCH_RESULT_LIMIT=100

# Celery配置
CELERY_BROKER_URL=redis://redis-host:6379/0
CELERY_RESULT_BACKEND=redis://redis-host:6379/0

# 安全配置
CORS_ORIGINS=https://your-domain.com
RATE_LIMIT_PER_MINUTE=100

# 监控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO
            </div>

            <h3>部署脚本</h3>

            <h4>自动化部署脚本</h4>
            <div class="python-code">
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e

echo "🚀 开始部署企业文件共享系统..."

# 1. 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 2. 构建Docker镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 3. 停止旧服务
echo "⏹️ 停止旧服务..."
docker-compose down

# 4. 启动新服务
echo "▶️ 启动新服务..."
docker-compose up -d

# 5. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 6. 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
docker-compose exec web alembic upgrade head

# 7. 创建Elasticsearch索引
echo "🔍 创建搜索索引..."
docker-compose exec web python -c "
from app.search.elasticsearch_config import create_file_index
from app.database import es_client
import asyncio
asyncio.run(create_file_index(es_client))
"

# 8. 健康检查
echo "🏥 健康检查..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health; then
        echo "✅ 服务启动成功!"
        break
    else
        echo "⏳ 等待服务启动... ($i/10)"
        sleep 10
    fi
done

# 9. 清理旧镜像
echo "🧹 清理旧镜像..."
docker image prune -f

echo "🎉 部署完成!"
echo "📊 查看服务状态: docker-compose ps"
echo "📋 查看日志: docker-compose logs -f"
            </div>
        </div>

        <!-- 代码示例 -->
        <div id="code" class="section">
            <h2>完整代码示例</h2>

            <h3>FastAPI路由示例</h3>

            <h4>文件管理路由</h4>
            <div class="python-code">
# routers/files.py
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Query
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import aiofiles
import zipfile
import io

router = APIRouter()

@router.post("/upload", summary="上传文件")
async def upload_files(
    files: List[UploadFile] = File(...),
    directory: str = Query("/", description="上传目录"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量上传文件"""

    # 检查上传权限
    if not await check_upload_permission(current_user.id, directory, db):
        raise HTTPException(403, "无上传权限")

    uploaded_files = []

    for file in files:
        try:
            # 验证文件
            if not is_allowed_file_type(file.filename):
                raise HTTPException(400, f"不支持的文件类型: {file.filename}")

            if file.size > settings.max_file_size:
                raise HTTPException(400, f"文件过大: {file.filename}")

            # 生成文件路径
            file_path = generate_file_path(directory, file.filename)

            # 异步保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)

            # 计算文件哈希
            file_hash = calculate_file_hash(content)

            # 检查重复文件
            existing_file = await get_file_by_hash(db, file_hash)
            if existing_file:
                # 创建硬链接而不是重复存储
                create_hard_link(existing_file.file_path, file_path)

            # 创建数据库记录
            file_entry = FileEntry(
                filename=file.filename,
                file_path=file_path,
                file_size=file.size,
                file_type=get_file_type(file.filename),
                file_hash=file_hash,
                mime_type=file.content_type,
                owner_id=current_user.id,
                directory_path=directory
            )

            db.add(file_entry)
            await db.flush()

            # 异步处理任务
            generate_thumbnail.delay(file_entry.id, file_path)
            extract_image_features.delay(file_entry.id, file_path)
            index_file_content.delay(file_entry.id)

            uploaded_files.append({
                "id": file_entry.id,
                "filename": file.filename,
                "size": file.size,
                "type": file_entry.file_type
            })

        except Exception as e:
            # 清理已上传的文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(500, f"上传失败: {str(e)}")

    await db.commit()

    # 记录上传日志
    await log_user_activity(
        current_user.id,
        "UPLOAD",
        {"files": len(uploaded_files), "directory": directory}
    )

    return {
        "message": f"成功上传 {len(uploaded_files)} 个文件",
        "files": uploaded_files
    }

@router.get("/download/batch", summary="批量下载")
async def batch_download(
    file_ids: List[int] = Query(..., description="文件ID列表"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量下载文件（ZIP压缩）"""

    # 检查下载权限
    accessible_files = []
    for file_id in file_ids:
        if await check_download_permission(current_user.id, file_id, db):
            file_entry = await db.get(FileEntry, file_id)
            if file_entry and os.path.exists(file_entry.file_path):
                accessible_files.append(file_entry)

    if not accessible_files:
        raise HTTPException(404, "没有可下载的文件")

    # 创建ZIP文件
    zip_buffer = io.BytesIO()

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for file_entry in accessible_files:
            # 添加文件到ZIP
            zip_file.write(file_entry.file_path, file_entry.filename)

    zip_buffer.seek(0)

    # 记录下载日志
    await log_batch_download(
        current_user.id,
        [f.id for f in accessible_files]
    )

    # 返回ZIP文件流
    return StreamingResponse(
        io.BytesIO(zip_buffer.read()),
        media_type="application/zip",
        headers={"Content-Disposition": "attachment; filename=files.zip"}
    )

@router.get("/{file_id}/thumbnail", summary="获取缩略图")
async def get_thumbnail(
    file_id: int,
    size: int = Query(300, description="缩略图尺寸"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取文件缩略图"""

    # 检查访问权限
    if not await check_read_permission(current_user.id, file_id, db):
        raise HTTPException(403, "无访问权限")

    # 获取缩略图
    thumbnail = await get_file_thumbnail(db, file_id, size)

    if not thumbnail or not os.path.exists(thumbnail.thumbnail_path):
        # 如果缩略图不存在，异步生成
        generate_thumbnail.delay(file_id, size)
        raise HTTPException(404, "缩略图生成中，请稍后重试")

    return FileResponse(
        thumbnail.thumbnail_path,
        media_type="image/jpeg"
    )
            </div>

            <h3>启动脚本</h3>

            <h4>开发环境启动</h4>
            <div class="python-code">
# run_dev.py - 开发环境启动脚本
import uvicorn
import asyncio
from app.main import app
from app.database import engine, Base
from app.search.elasticsearch_config import create_file_index, es_client

async def init_database():
    """初始化数据库"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

async def init_elasticsearch():
    """初始化Elasticsearch"""
    await create_file_index(es_client)

async def startup():
    """启动初始化"""
    print("🚀 初始化数据库...")
    await init_database()

    print("🔍 初始化搜索引擎...")
    await init_elasticsearch()

    print("✅ 初始化完成!")

if __name__ == "__main__":
    # 运行初始化
    asyncio.run(startup())

    # 启动开发服务器
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
            </div>

            <h4>生产环境启动</h4>
            <div class="python-code">
# gunicorn.conf.py - Gunicorn配置
import multiprocessing

# 服务器配置
bind = "0.0.0.0:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 日志配置
accesslog = "/app/logs/access.log"
errorlog = "/app/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
preload_app = True
timeout = 120
keepalive = 5

# 启动脚本
# gunicorn -c gunicorn.conf.py app.main:app
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // 隐藏所有section
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(link => {
                link.style.background = '';
                link.style.color = '#3776ab';
            });

            event.target.style.background = '#3776ab';
            event.target.style.color = 'white';
        }

        // 默认显示第一个section
        document.addEventListener('DOMContentLoaded', function() {
            showSection('overview');
        });
    </script>
</body>
</html>
